#!/usr/bin/env python3
"""
Simple test to check complexity calculation
"""

# Manual calculation for: "Create a very creative website about plants, including a blog system"
prompt = "Create a very creative website about plants, including a blog system"
prompt_lower = prompt.lower()

print(f"Testing: {prompt}")
print(f"Lowercase: {prompt_lower}")

# Workflow indicators (flexible patterns)
workflow_indicators = [
    "website", "web site", "web page", "homepage", "site",
    "application", "app", "software", "program",
    "project", "system", "platform",
    "multiple", "several", "complete", "full", "entire", "whole",
    "implement", "develop", "build", "create", "make", "design",
    "from scratch", "custom", "professional",
    "blog", "cms", "dashboard", "admin", "portal", "gallery",
    "responsive", "interactive", "dynamic", "animated",
    "with styling", "with css", "with javascript", "with pages",
    "including", "featuring", "containing"
]

matches = [indicator for indicator in workflow_indicators if indicator in prompt_lower]
print(f"Workflow matches: {matches}")

# Base score
base_score = min(len(matches) * 0.4, 0.9)
print(f"Base score: {base_score}")

# Create boost
has_create = any(word in prompt_lower for word in ["create", "make", "build"])
has_target = any(word in prompt_lower for word in ["website", "app", "project"])
create_boost = 0.3 if (has_create and has_target) else 0.0
print(f"Create boost: {create_boost} (create={has_create}, target={has_target})")

# Complexity factors
complexity_factors = [
    ("blog", 0.3), ("creative", 0.15), ("including", 0.2), ("system", 0.3)
]

factor_score = 0
for factor, weight in complexity_factors:
    if factor in prompt_lower:
        factor_score += weight
        print(f"Found factor: {factor} (+{weight})")

print(f"Factor score: {factor_score}")

total_score = min(base_score + create_boost + factor_score, 1.0)
print(f"TOTAL SCORE: {total_score}")
print(f"THRESHOLD: 0.4")
print(f"WORKFLOW REQUIRED: {total_score >= 0.4}")
