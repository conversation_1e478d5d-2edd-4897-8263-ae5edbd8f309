{% extends "base.html" %}

{% block title %}Chat - Flask AI App{% endblock %}

{% block content %}
<!-- Content Grid: Full available space between nav and footer -->
<div class="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-6 p-6 min-h-0">
        <!-- Left Panel: Chat Interface -->
        <div class="bg-white rounded-lg shadow-sm border flex flex-col min-h-0">
            <!-- Chat Header: Fixed height -->
            <div class="border-b p-4 flex-shrink-0">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">AI Chat</h2>
                        <p class="text-sm text-gray-600">Ask the AI anything or use it to help with your code</p>
                    </div>
                    <div class="flex space-x-2">
                        <button
                            id="new-conversation-button"
                            class="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                            title="Start new conversation"
                        >
                            New Chat
                        </button>
                        <button
                            id="conversations-list-button"
                            class="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700"
                            title="View conversation history"
                        >
                            History
                        </button>
                    </div>
                </div>
            </div>

            <!-- Chat Messages: Scrollable area that takes remaining space -->
            <div id="chat-messages" class="flex-1 overflow-y-auto p-4 space-y-4 min-h-0">
                <!-- Messages will be added here dynamically -->
                <div class="text-center text-gray-500 py-8">
                    <p>Start a conversation with the AI!</p>
                </div>
            </div>

            <!-- Chat Input: Fixed height at bottom -->
            <div class="border-t p-4 flex-shrink-0">
                <!-- Attached Files Display -->
                <div id="attached-files" class="hidden mb-3 p-2 bg-blue-50 rounded border"></div>

                <form id="chat-form" class="flex space-x-2">
                    <div class="flex-1">
                        <textarea
                            id="chat-input"
                            placeholder="Ask the AI..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                            rows="2"
                            style="max-height: 120px; overflow-y: auto; min-height: 44px;"
                        ></textarea>
                    </div>
                    <div class="flex flex-col space-y-2">
                        <button
                            type="submit"
                            id="send-button"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                        >
                            Send
                        </button>
                        <button
                            type="button"
                            id="attach-file-button"
                            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
                            title="Attach file"
                        >
                            📎
                        </button>
                    </div>
                </form>

                <!-- Hidden file input -->
                <input
                    type="file"
                    id="file-input"
                    multiple
                    accept=".txt,.py,.js,.html,.css,.json,.xml,.yaml,.yml,.md,.rst,.pdf,.doc,.docx,.png,.jpg,.jpeg,.gif,.svg,.csv,.tsv"
                    class="hidden"
                >

                <!-- Model Selection -->
                <div class="mt-2 flex items-center space-x-2">
                    <label for="model-select" class="text-sm text-gray-600">Model:</label>
                    <select id="model-select" class="text-sm border border-gray-300 rounded px-2 py-1">
                        <option value="deepseek/deepseek-r1-0528:free" selected>DeepSeek R1 (Free) 🆓</option>
                        <option value="deepseek/deepseek-chat">DeepSeek Chat</option>
                        <option value="openai/gpt-3.5-turbo">GPT-3.5 Turbo</option>
                        <option value="openai/gpt-4">GPT-4</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Right Panel: File Editor -->
        <div class="bg-white rounded-lg shadow-sm border flex flex-col min-h-0">
            <!-- Editor Header: Fixed height -->
            <div class="border-b p-4 flex items-center justify-between flex-shrink-0">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">📝 AI Code Editor</h2>
                    <p class="text-sm text-gray-600" id="current-file-path">AI will create and edit files here</p>
                </div>
                <div class="flex space-x-2">
                    <button
                        id="new-file-button"
                        class="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                        title="Create new file"
                    >
                        New
                    </button>
                    <button
                        id="open-file-button"
                        class="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700"
                    >
                        Open
                    </button>
                    <button
                        id="save-file-button"
                        class="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
                        disabled
                    >
                        Save
                    </button>
                </div>
            </div>

            <!-- File Tree (collapsible): Fixed height when visible -->
            <div id="file-tree-container" class="border-b bg-gray-50 max-h-32 overflow-y-auto hidden flex-shrink-0">
                <div class="p-2">
                    <div class="text-sm font-medium text-gray-700 mb-2">Project Files</div>
                    <div id="file-tree" class="text-sm">
                        <!-- File tree will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Code Editor: Takes remaining space -->
            <div class="flex-1 relative min-h-0">
                <textarea
                    id="code-editor"
                    class="w-full h-full p-4 font-mono text-sm border-none resize-none focus:outline-none code-editor"
                    placeholder="Open a file or start typing..."
                ></textarea>

                <!-- Editor Status Bar -->
                <div class="absolute bottom-0 right-0 bg-gray-100 px-2 py-1 text-xs text-gray-600 border-l border-t">
                    <span id="editor-status">Ready</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- File Selection Modal -->
<div id="file-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full">
            <div class="p-4 border-b">
                <h3 id="modal-title" class="text-lg font-semibold">Select File</h3>
            </div>
            <div class="p-4">
                <input
                    type="text"
                    id="file-path-input"
                    placeholder="Enter file path (e.g., app.py)"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                <div class="mt-4 flex justify-end space-x-2">
                    <button
                        id="cancel-file-button"
                        class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                        Cancel
                    </button>
                    <button
                        id="load-file-button"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                        Load File
                    </button>
                    <button
                        id="create-file-button"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 hidden"
                    >
                        Create File
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Conversation History Modal -->
<div id="conversations-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] flex flex-col">
            <div class="p-4 border-b flex items-center justify-between">
                <h3 class="text-lg font-semibold">Conversation History</h3>
                <button
                    id="close-conversations-button"
                    class="text-gray-400 hover:text-gray-600"
                >
                    ✕
                </button>
            </div>
            <div class="flex-1 overflow-y-auto p-4">
                <div id="conversations-list" class="space-y-2">
                    <!-- Conversations will be loaded here -->
                    <div class="text-center text-gray-500 py-8">
                        <p>Loading conversations...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- External JavaScript Files -->
<script src="{{ url_for('static', filename='js/app-utils.js') }}"></script>
<script src="{{ url_for('static', filename='js/chat.js') }}"></script>
<script src="{{ url_for('static', filename='js/file-editor.js') }}"></script>

<!-- Legacy inline scripts (to be removed after testing) -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Chat functionality
    const chatForm = document.getElementById('chat-form');
    const chatInput = document.getElementById('chat-input');
    const chatMessages = document.getElementById('chat-messages');
    const sendButton = document.getElementById('send-button');
    const modelSelect = document.getElementById('model-select');
    
    // File editor functionality
    const codeEditor = document.getElementById('code-editor');
    const currentFilePath = document.getElementById('current-file-path');
    const openFileButton = document.getElementById('open-file-button');
    const saveFileButton = document.getElementById('save-file-button');
    const editorStatus = document.getElementById('editor-status');
    
    // File modal
    const fileModal = document.getElementById('file-modal');
    const filePathInput = document.getElementById('file-path-input');
    const loadFileButton = document.getElementById('load-file-button');
    const cancelFileButton = document.getElementById('cancel-file-button');

    // Conversation management
    const newConversationButton = document.getElementById('new-conversation-button');
    const conversationsListButton = document.getElementById('conversations-list-button');
    const conversationsModal = document.getElementById('conversations-modal');
    const closeConversationsButton = document.getElementById('close-conversations-button');
    const conversationsList = document.getElementById('conversations-list');

    let currentFile = null;
    let conversationId = localStorage.getItem('currentConversationId') || null;
    
    // Chat functionality
    chatForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const message = chatInput.value.trim();
        if (!message) return;
        
        // Add user message to chat
        addMessage('user', message);
        
        // Clear input and disable send button
        chatInput.value = '';
        sendButton.disabled = true;
        sendButton.innerHTML = '<div class="spinner"></div>';
        
        try {
            // Send to AI
            const response = await AppUtils.apiRequest('/api/llm/query', {
                method: 'POST',
                body: JSON.stringify({
                    prompt: message,
                    model: modelSelect.value,
                    conversation_id: conversationId
                })
            });
            
            // Update conversation ID
            conversationId = response.conversation_id;
            localStorage.setItem('currentConversationId', conversationId);

            // Add AI response to chat
            addMessage('assistant', response.response);
            
        } catch (error) {
            addMessage('system', 'Error: Failed to get AI response');
        } finally {
            sendButton.disabled = false;
            sendButton.innerHTML = 'Send';
        }
    });
    
    function addMessage(role, content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${
            role === 'user' ? 'ml-8' : 
            role === 'assistant' ? 'mr-8' : 
            'mx-4'
        }`;
        
        const roleColor = {
            'user': 'bg-blue-100 text-blue-800',
            'assistant': 'bg-green-100 text-green-800',
            'system': 'bg-red-100 text-red-800'
        }[role];
        
        messageDiv.innerHTML = `
            <div class="flex items-start space-x-2">
                <div class="px-2 py-1 rounded text-xs font-medium ${roleColor}">
                    ${role.charAt(0).toUpperCase() + role.slice(1)}
                </div>
                <div class="flex-1 bg-gray-50 rounded-lg p-3">
                    <pre class="whitespace-pre-wrap text-sm">${content}</pre>
                </div>
            </div>
        `;
        
        // Remove placeholder if it exists
        const placeholder = chatMessages.querySelector('.text-center');
        if (placeholder) {
            placeholder.remove();
        }
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Conversation management functions
    function startNewConversation() {
        conversationId = null;
        localStorage.removeItem('currentConversationId');

        // Clear chat messages
        chatMessages.innerHTML = `
            <div class="text-center text-gray-500 py-8">
                <p>Start a conversation with the AI!</p>
            </div>
        `;

        AppUtils.showNotification('Started new conversation', 'success');
    }

    async function loadConversationHistory(convId) {
        try {
            const messages = await AppUtils.apiRequest(`/api/llm/conversations/${convId}`);

            // Clear current messages
            chatMessages.innerHTML = '';

            // Add all messages from history
            messages.forEach(msg => {
                addMessage(msg.role, msg.content);
            });

            // Set current conversation ID
            conversationId = convId;
            localStorage.setItem('currentConversationId', conversationId);

            // Close modal
            conversationsModal.classList.add('hidden');

            AppUtils.showNotification('Conversation loaded', 'success');

        } catch (error) {
            AppUtils.showNotification('Failed to load conversation', 'error');
        }
    }

    async function loadConversationsList() {
        try {
            conversationsList.innerHTML = '<div class="text-center text-gray-500 py-4">Loading...</div>';

            const response = await AppUtils.apiRequest('/api/llm/conversations');
            const conversations = response.conversations;

            if (conversations.length === 0) {
                conversationsList.innerHTML = `
                    <div class="text-center text-gray-500 py-8">
                        <p>No conversations yet</p>
                        <p class="text-sm">Start chatting to create your first conversation!</p>
                    </div>
                `;
                return;
            }

            conversationsList.innerHTML = '';

            conversations.forEach(conv => {
                const convDiv = document.createElement('div');
                convDiv.className = 'border rounded-lg p-3 hover:bg-gray-50 cursor-pointer';

                const isActive = conv.conversation_id === conversationId;
                if (isActive) {
                    convDiv.classList.add('bg-blue-50', 'border-blue-200');
                }

                convDiv.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="text-sm font-medium text-gray-900">
                                ${isActive ? '🔵 ' : ''}Conversation ${conv.conversation_id.substring(0, 8)}...
                            </div>
                            <div class="text-xs text-gray-500">
                                ${conv.message_count} messages • ${conv.total_tokens} tokens
                                ${conv.total_cost > 0 ? ` • $${conv.total_cost.toFixed(4)}` : ''}
                            </div>
                            <div class="text-xs text-gray-400">
                                ${new Date(conv.last_message_at).toLocaleString()}
                            </div>
                        </div>
                        <div class="flex space-x-1">
                            <button
                                class="load-conversation-btn px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                                data-conversation-id="${conv.conversation_id}"
                            >
                                Load
                            </button>
                            <button
                                class="delete-conversation-btn px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
                                data-conversation-id="${conv.conversation_id}"
                            >
                                Delete
                            </button>
                        </div>
                    </div>
                `;

                conversationsList.appendChild(convDiv);
            });

            // Add event listeners for load and delete buttons
            document.querySelectorAll('.load-conversation-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const convId = btn.dataset.conversationId;
                    loadConversationHistory(convId);
                });
            });

            document.querySelectorAll('.delete-conversation-btn').forEach(btn => {
                btn.addEventListener('click', async (e) => {
                    e.stopPropagation();
                    const convId = btn.dataset.conversationId;

                    if (confirm('Are you sure you want to delete this conversation?')) {
                        try {
                            await AppUtils.apiRequest(`/api/llm/conversations/${convId}`, {
                                method: 'DELETE'
                            });

                            // If this was the current conversation, start a new one
                            if (convId === conversationId) {
                                startNewConversation();
                            }

                            // Reload the conversations list
                            loadConversationsList();

                            AppUtils.showNotification('Conversation deleted', 'success');
                        } catch (error) {
                            AppUtils.showNotification('Failed to delete conversation', 'error');
                        }
                    }
                });
            });

        } catch (error) {
            conversationsList.innerHTML = `
                <div class="text-center text-red-500 py-8">
                    <p>Failed to load conversations</p>
                </div>
            `;
        }
    }

    // Conversation management event listeners
    newConversationButton.addEventListener('click', startNewConversation);

    conversationsListButton.addEventListener('click', function() {
        conversationsModal.classList.remove('hidden');
        loadConversationsList();
    });

    closeConversationsButton.addEventListener('click', function() {
        conversationsModal.classList.add('hidden');
    });

    // Close modal when clicking outside
    conversationsModal.addEventListener('click', function(e) {
        if (e.target === conversationsModal) {
            conversationsModal.classList.add('hidden');
        }
    });

    // Load conversation history on page load if there's a current conversation
    async function loadCurrentConversation() {
        if (conversationId) {
            try {
                const messages = await AppUtils.apiRequest(`/api/llm/conversations/${conversationId}`);

                if (messages.length > 0) {
                    // Clear placeholder
                    chatMessages.innerHTML = '';

                    // Add all messages from history
                    messages.forEach(msg => {
                        addMessage(msg.role, msg.content);
                    });

                    AppUtils.showNotification('Previous conversation restored', 'info');
                }
            } catch (error) {
                // If conversation doesn't exist anymore, start fresh
                startNewConversation();
            }
        }
    }

    // Load current conversation on page load
    loadCurrentConversation();

    // File editor functionality
    openFileButton.addEventListener('click', async function() {
        fileModal.classList.remove('hidden');
        filePathInput.focus();

        // Load project files for browsing
        await loadProjectFilesForBrowser();
    });
    
    cancelFileButton.addEventListener('click', function() {
        fileModal.classList.add('hidden');
        filePathInput.value = '';
        // Clear file browser if it exists
        const fileBrowser = fileModal.querySelector('.file-browser');
        if (fileBrowser) {
            fileBrowser.remove();
        }
    });

    // Function to load project files for browsing
    async function loadProjectFilesForBrowser() {
        try {
            // Get current project files using the list-workspace endpoint
            const response = await AppUtils.apiRequest('/api/ai-coder/list-workspace?directory=.');

            if (response.success && response.content) {
                const files = JSON.parse(response.content);
                // Filter to only show files (not directories) for the file browser
                const fileList = files.filter(item => item.is_file);
                displayFileBrowserInModal(fileList);
            }
        } catch (error) {
            console.error('Failed to load project files:', error);
        }
    }

    // Function to display file browser in modal
    function displayFileBrowserInModal(files) {
        // Remove existing file browser
        const existingBrowser = fileModal.querySelector('.file-browser');
        if (existingBrowser) {
            existingBrowser.remove();
        }

        // Create file browser
        const fileBrowser = document.createElement('div');
        fileBrowser.className = 'file-browser mt-4 max-h-64 overflow-y-auto border border-gray-300 rounded-lg';

        const browserTitle = document.createElement('div');
        browserTitle.className = 'bg-gray-100 px-3 py-2 border-b border-gray-300 font-medium text-sm';
        browserTitle.textContent = 'Project Files (click to select):';
        fileBrowser.appendChild(browserTitle);

        const fileList = document.createElement('div');
        fileList.className = 'divide-y divide-gray-200';

        if (files.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'px-3 py-4 text-gray-500 text-center text-sm';
            emptyMessage.textContent = 'No files in current project';
            fileList.appendChild(emptyMessage);
        } else {
            files.forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.className = 'px-3 py-2 hover:bg-blue-50 cursor-pointer flex items-center space-x-2 text-sm';

                const fileIcon = document.createElement('span');
                fileIcon.textContent = getFileIconForBrowser(file.name);

                const fileName = document.createElement('span');
                fileName.textContent = file.name;
                fileName.className = 'flex-1';

                const fileSize = document.createElement('span');
                fileSize.textContent = AppUtils.formatFileSize(file.size);
                fileSize.className = 'text-gray-500 text-xs';

                fileItem.appendChild(fileIcon);
                fileItem.appendChild(fileName);
                fileItem.appendChild(fileSize);

                fileItem.addEventListener('click', () => {
                    filePathInput.value = file.name;
                    // Highlight selected file
                    fileList.querySelectorAll('.file-item').forEach(item => {
                        item.classList.remove('bg-blue-100');
                    });
                    fileItem.classList.add('bg-blue-100');
                });

                fileItem.classList.add('file-item');
                fileList.appendChild(fileItem);
            });
        }

        fileBrowser.appendChild(fileList);

        // Insert file browser after the input field
        const inputContainer = filePathInput.parentElement;
        inputContainer.appendChild(fileBrowser);
    }

    // Function to get file icon
    function getFileIconForBrowser(fileName) {
        const extension = '.' + fileName.split('.').pop().toLowerCase();
        const icons = {
            '.py': '🐍',
            '.js': '📜',
            '.html': '🌐',
            '.css': '🎨',
            '.json': '📋',
            '.md': '📝',
            '.txt': '📄',
            '.yaml': '⚙️',
            '.yml': '⚙️',
            '.sql': '🗃️',
            '.png': '🖼️',
            '.jpg': '🖼️',
            '.jpeg': '🖼️',
            '.gif': '🖼️',
            '.svg': '🖼️',
            '.pdf': '📕'
        };
        return icons[extension] || '📄';
    }
    
    loadFileButton.addEventListener('click', async function() {
        const filePath = filePathInput.value.trim();
        if (!filePath) return;

        try {
            editorStatus.textContent = 'Loading...';

            // Use project-aware API endpoint
            const response = await AppUtils.apiRequest('/api/ai-coder/file-operation', {
                method: 'POST',
                body: JSON.stringify({
                    operation: 'read',
                    file_path: filePath
                })
            });

            if (response.success) {
                codeEditor.value = response.content;
                currentFilePath.textContent = response.file_path;
                currentFile = response.file_path;
                saveFileButton.disabled = false;
                editorStatus.textContent = `Loaded (${response.file_size || response.content.length} bytes)`;

                fileModal.classList.add('hidden');
                filePathInput.value = '';

                AppUtils.showNotification(`File loaded: ${response.file_path}`, 'success');
            } else {
                throw new Error(response.error || response.message || 'Failed to load file');
            }

        } catch (error) {
            editorStatus.textContent = 'Error loading file';
            AppUtils.showNotification(`Failed to load file: ${error.message}`, 'error');
        }
    });
    
    saveFileButton.addEventListener('click', async function() {
        if (!currentFile) return;
        
        try {
            editorStatus.textContent = 'Saving...';
            
            // Use project-aware API endpoint
            const response = await AppUtils.apiRequest('/api/ai-coder/file-operation', {
                method: 'POST',
                body: JSON.stringify({
                    operation: 'write',
                    file_path: currentFile,
                    content: codeEditor.value
                })
            });

            if (response.success) {
                editorStatus.textContent = `Saved (${response.file_size || codeEditor.value.length} bytes)`;
                AppUtils.showNotification('File saved successfully!', 'success');
            } else {
                throw new Error(response.error || response.message || 'Failed to save file');
            }
            
        } catch (error) {
            editorStatus.textContent = 'Error saving file';
        }
    });
    
    // Handle Enter key in file path input
    filePathInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            loadFileButton.click();
        }
    });
    
    // Handle Ctrl+S for saving
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            if (!saveFileButton.disabled) {
                saveFileButton.click();
            }
        }
    });
    
    // Auto-save indicator
    let saveTimeout;
    codeEditor.addEventListener('input', function() {
        if (currentFile) {
            editorStatus.textContent = 'Modified';
            
            // Clear existing timeout
            clearTimeout(saveTimeout);
            
            // Set new timeout for auto-save indicator
            saveTimeout = setTimeout(() => {
                editorStatus.textContent = 'Ready';
            }, 2000);
        }
    });
});
</script>
{% endblock %}
