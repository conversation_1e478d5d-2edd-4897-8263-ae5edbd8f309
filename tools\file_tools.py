"""
File manipulation tools with Pydantic validation
"""
import os
import hashlib
import mimetypes
from pathlib import Path
from typing import Optional, List
from datetime import datetime, timezone
from pydantic import BaseModel, Field, field_validator
from flask import current_app
import logging

logger = logging.getLogger(__name__)

# Pydantic models for file operations
class FileReadRequest(BaseModel):
    file_path: str = Field(..., description="Path to the file to read")
    encoding: str = Field(default="utf-8", description="File encoding")
    
    @field_validator('file_path')
    @classmethod
    def validate_file_path(cls, v):
        if not v or not v.strip():
            raise ValueError("File path cannot be empty")

        # Security check - prevent path traversal
        if '..' in v or v.startswith('/'):
            raise ValueError("Invalid file path - path traversal not allowed")

        return v.strip()

class FileReadResponse(BaseModel):
    content: str = Field(..., description="File content")
    file_path: str = Field(..., description="Path of the read file")
    file_size: int = Field(..., description="File size in bytes")
    mime_type: Optional[str] = Field(None, description="MIME type of the file")
    encoding: str = Field(..., description="File encoding used")

class FileWriteRequest(BaseModel):
    file_path: str = Field(..., description="Path to write the file")
    content: str = Field(..., description="Content to write")
    encoding: str = Field(default="utf-8", description="File encoding")
    create_dirs: bool = Field(default=True, description="Create directories if they don't exist")
    
    @field_validator('file_path')
    @classmethod
    def validate_file_path(cls, v):
        if not v or not v.strip():
            raise ValueError("File path cannot be empty")

        # Security check - prevent path traversal
        if '..' in v or v.startswith('/'):
            raise ValueError("Invalid file path - path traversal not allowed")

        return v.strip()

class FileWriteResponse(BaseModel):
    file_path: str = Field(..., description="Path of the written file")
    bytes_written: int = Field(..., description="Number of bytes written")
    success: bool = Field(..., description="Whether the write was successful")

class FileListRequest(BaseModel):
    directory: str = Field(default=".", description="Directory to list")
    pattern: Optional[str] = Field(None, description="File pattern to match (glob)")
    recursive: bool = Field(default=False, description="List files recursively")
    include_hidden: bool = Field(default=False, description="Include hidden files")
    
    @field_validator('directory')
    @classmethod
    def validate_directory(cls, v):
        if not v:
            v = "."

        # Security check - prevent path traversal
        if '..' in v or v.startswith('/'):
            raise ValueError("Invalid directory path - path traversal not allowed")

        return v.strip()

class FileInfo(BaseModel):
    name: str = Field(..., description="File name")
    path: str = Field(..., description="Full file path")
    size: int = Field(..., description="File size in bytes")
    is_directory: bool = Field(..., description="Whether this is a directory")
    mime_type: Optional[str] = Field(None, description="MIME type")
    modified_time: str = Field(..., description="Last modified time (ISO format)")

class FileListResponse(BaseModel):
    files: List[FileInfo] = Field(..., description="List of files")
    directory: str = Field(..., description="Directory that was listed")
    total_count: int = Field(..., description="Total number of files found")

class FileTools:
    """File manipulation tools with validation and security checks"""
    
    @staticmethod
    def _get_file_hash(content: str) -> str:
        """Generate SHA-256 hash of file content"""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    @staticmethod
    def _is_allowed_file(file_path: str) -> bool:
        """Check if file extension is allowed"""
        if not hasattr(current_app, 'config'):
            return True  # Allow all files if no config
        
        allowed_extensions = current_app.config.get('ALLOWED_FILE_EXTENSIONS', set())
        if not allowed_extensions:
            return True  # Allow all files if no restrictions
        
        file_ext = Path(file_path).suffix.lower()
        return file_ext in allowed_extensions
    
    @staticmethod
    def _check_file_size(content: str) -> bool:
        """Check if file size is within limits"""
        if not hasattr(current_app, 'config'):
            return True  # Allow any size if no config
        
        max_size = current_app.config.get('MAX_FILE_SIZE', 10 * 1024 * 1024)  # 10MB default
        content_size = len(content.encode('utf-8'))
        return content_size <= max_size
    
    @classmethod
    async def read_file(cls, request: FileReadRequest) -> FileReadResponse:
        """Read file content with validation"""
        try:
            # Security checks
            if not cls._is_allowed_file(request.file_path):
                raise ValueError(f"File type not allowed: {request.file_path}")
            
            # Check if file exists
            if not os.path.exists(request.file_path):
                raise FileNotFoundError(f"File not found: {request.file_path}")
            
            # Read file content
            with open(request.file_path, 'r', encoding=request.encoding) as f:
                content = f.read()
            
            # Check file size
            if not cls._check_file_size(content):
                raise ValueError("File size exceeds maximum allowed size")
            
            # Get file info
            file_size = os.path.getsize(request.file_path)
            mime_type, _ = mimetypes.guess_type(request.file_path)
            
            logger.info(f"Successfully read file: {request.file_path} ({file_size} bytes)")
            
            return FileReadResponse(
                content=content,
                file_path=request.file_path,
                file_size=file_size,
                mime_type=mime_type,
                encoding=request.encoding
            )
            
        except Exception as e:
            logger.error(f"Error reading file {request.file_path}: {e}")
            raise
    
    @classmethod
    async def write_file(cls, request: FileWriteRequest) -> FileWriteResponse:
        """Write file content with validation"""
        try:
            # Security checks
            if not cls._is_allowed_file(request.file_path):
                raise ValueError(f"File type not allowed: {request.file_path}")
            
            # Check content size
            if not cls._check_file_size(request.content):
                raise ValueError("Content size exceeds maximum allowed size")
            
            # Create directories if needed
            if request.create_dirs:
                dir_path = os.path.dirname(request.file_path)
                if dir_path:  # Only create if there's actually a directory path
                    os.makedirs(dir_path, exist_ok=True)
            
            # Write file
            with open(request.file_path, 'w', encoding=request.encoding) as f:
                f.write(request.content)
            
            bytes_written = len(request.content.encode(request.encoding))
            
            logger.info(f"Successfully wrote file: {request.file_path} ({bytes_written} bytes)")
            
            return FileWriteResponse(
                file_path=request.file_path,
                bytes_written=bytes_written,
                success=True
            )
            
        except Exception as e:
            logger.error(f"Error writing file {request.file_path}: {e}")
            raise
    
    @classmethod
    async def list_files(cls, request: FileListRequest) -> FileListResponse:
        """List files in directory with validation"""
        try:
            # Check if directory exists
            if not os.path.exists(request.directory):
                raise FileNotFoundError(f"Directory not found: {request.directory}")
            
            if not os.path.isdir(request.directory):
                raise ValueError(f"Path is not a directory: {request.directory}")
            
            files = []
            
            if request.recursive:
                # Recursive listing
                for root, dirs, filenames in os.walk(request.directory):
                    # Filter hidden directories if needed
                    if not request.include_hidden:
                        dirs[:] = [d for d in dirs if not d.startswith('.')]
                    
                    for filename in filenames:
                        # Filter hidden files if needed
                        if not request.include_hidden and filename.startswith('.'):
                            continue
                        
                        file_path = os.path.join(root, filename)
                        files.append(cls._get_file_info(file_path))
            else:
                # Non-recursive listing
                for item in os.listdir(request.directory):
                    # Filter hidden files if needed
                    if not request.include_hidden and item.startswith('.'):
                        continue
                    
                    item_path = os.path.join(request.directory, item)
                    files.append(cls._get_file_info(item_path))
            
            # Apply pattern filter if specified
            if request.pattern:
                import fnmatch
                files = [f for f in files if fnmatch.fnmatch(f.name, request.pattern)]
            
            logger.info(f"Listed {len(files)} files in directory: {request.directory}")
            
            return FileListResponse(
                files=files,
                directory=request.directory,
                total_count=len(files)
            )
            
        except Exception as e:
            logger.error(f"Error listing files in {request.directory}: {e}")
            raise
    
    @staticmethod
    def _get_file_info(file_path: str) -> FileInfo:
        """Get file information"""
        stat = os.stat(file_path)
        mime_type, _ = mimetypes.guess_type(file_path)
        
        return FileInfo(
            name=os.path.basename(file_path),
            path=file_path,
            size=stat.st_size,
            is_directory=os.path.isdir(file_path),
            mime_type=mime_type,
            modified_time=datetime.fromtimestamp(stat.st_mtime, timezone.utc).isoformat()
        )
