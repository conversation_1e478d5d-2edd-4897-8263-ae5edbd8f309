#!/usr/bin/env python3
"""
Test Flask API endpoints
"""
import asyncio
import httpx
import json

async def test_flask_endpoints():
    """Test Flask API endpoints"""
    base_url = "http://localhost:5000"
    
    print("Flask API Endpoint Test")
    print("=" * 40)
    
    try:
        # Test 1: Check if Flask app is running
        print("🌐 Testing Flask app connection...")
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(f"{base_url}/")
            
            if response.status_code == 200:
                print("✅ Flask app is running!")
            else:
                print(f"❌ Flask app not responding: {response.status_code}")
                return False
        
        # Test 2: Test file read endpoint
        print("\n📁 Testing file read endpoint...")
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(
                f"{base_url}/api/tools/read_file",
                headers={"Content-Type": "application/json"},
                json={"file_path": "demo_file.py"}
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ File read working! File size: {result.get('file_size', 'Unknown')} bytes")
            else:
                print(f"❌ File read failed: {response.status_code}")
                print(f"Response: {response.text}")
        
        # Test 3: Test LLM endpoint
        print("\n🤖 Testing LLM endpoint...")
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{base_url}/api/llm/query",
                headers={"Content-Type": "application/json"},
                json={
                    "prompt": "Hello! Please respond with 'Flask API test successful!'",
                    "model": "deepseek/deepseek-chat"
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ LLM endpoint working!")
                print(f"📝 Response: {result.get('response', 'No response')}")
                print(f"🔢 Tokens: {result.get('tokens_used', 'Unknown')}")
            else:
                print(f"❌ LLM endpoint failed: {response.status_code}")
                print(f"Response: {response.text}")
                
                # Try to parse error details
                try:
                    error_data = response.json()
                    print(f"Error details: {error_data}")
                except:
                    pass
        
        # Test 4: Test diff endpoint
        print("\n🔄 Testing diff endpoint...")
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(
                f"{base_url}/api/tools/edit_diff",
                headers={"Content-Type": "application/json"},
                json={
                    "original": "Hello World",
                    "modified": "Hello Flask!"
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Diff endpoint working!")
                print(f"📊 Changes detected: {result.get('has_changes', False)}")
            else:
                print(f"❌ Diff endpoint failed: {response.status_code}")
                print(f"Response: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Flask API: {e}")
        return False

async def main():
    """Main test function"""
    success = await test_flask_endpoints()
    
    if success:
        print("\n🎉 Flask API tests completed!")
    else:
        print("\n❌ Some Flask API tests failed.")
        print("Make sure the Flask app is running on http://localhost:5000")

if __name__ == "__main__":
    asyncio.run(main())
