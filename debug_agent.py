#!/usr/bin/env python3
"""
Debug the unified agent
"""
import asyncio
import traceback
from tools.unified_agent import UnifiedAIAgent

async def debug_agent():
    """Debug the unified agent"""
    try:
        print("🔍 Creating unified agent...")
        agent = UnifiedAIAgent()
        print("✅ Agent created successfully!")
        
        print("\n🔍 Testing simple request...")
        result = await agent.process_request("Hello!")
        print(f"✅ Result: {result}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print(f"📋 Traceback:")
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_agent())
