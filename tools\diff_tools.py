"""
Diff and text comparison tools
"""
import difflib
import re
from typing import List, Optional, Tuple
from pydantic import BaseModel, Field, field_validator
import logging

logger = logging.getLogger(__name__)

# Pydantic models for diff operations
class DiffRequest(BaseModel):
    original: str = Field(..., description="Original text")
    modified: str = Field(..., description="Modified text")
    context_lines: int = Field(default=3, description="Number of context lines")
    ignore_whitespace: bool = Field(default=False, description="Ignore whitespace differences")
    ignore_case: bool = Field(default=False, description="Ignore case differences")
    
    @field_validator('context_lines')
    @classmethod
    def validate_context_lines(cls, v):
        if v < 0:
            raise ValueError("Context lines must be non-negative")
        return v

class DiffResponse(BaseModel):
    diff: str = Field(..., description="Unified diff output")
    has_changes: bool = Field(..., description="Whether there are any changes")
    additions: int = Field(..., description="Number of added lines")
    deletions: int = Field(..., description="Number of deleted lines")
    similarity_ratio: float = Field(..., description="Similarity ratio (0.0 to 1.0)")

class LineDiff(BaseModel):
    line_number: int = Field(..., description="Line number")
    change_type: str = Field(..., description="Type of change: 'added', 'deleted', 'modified', 'unchanged'")
    original_line: Optional[str] = Field(None, description="Original line content")
    modified_line: Optional[str] = Field(None, description="Modified line content")

class DetailedDiffResponse(BaseModel):
    line_diffs: List[LineDiff] = Field(..., description="Line-by-line diff details")
    summary: DiffResponse = Field(..., description="Summary of changes")

class SideBySideDiffRequest(BaseModel):
    original: str = Field(..., description="Original text")
    modified: str = Field(..., description="Modified text")
    line_width: int = Field(default=80, description="Maximum line width for display")
    ignore_whitespace: bool = Field(default=False, description="Ignore whitespace differences")
    
    @field_validator('line_width')
    @classmethod
    def validate_line_width(cls, v):
        if v < 20:
            raise ValueError("Line width must be at least 20 characters")
        return v

class SideBySideLine(BaseModel):
    line_number: int = Field(..., description="Line number")
    original: Optional[str] = Field(None, description="Original line")
    modified: Optional[str] = Field(None, description="Modified line")
    change_type: str = Field(..., description="Type of change")

class SideBySideDiffResponse(BaseModel):
    lines: List[SideBySideLine] = Field(..., description="Side-by-side comparison lines")
    summary: DiffResponse = Field(..., description="Summary of changes")

class DiffTools:
    """Text comparison and diff generation tools"""
    
    @staticmethod
    def _preprocess_text(text: str, ignore_whitespace: bool = False, ignore_case: bool = False) -> str:
        """Preprocess text based on options"""
        if ignore_case:
            text = text.lower()
        
        if ignore_whitespace:
            # Normalize whitespace
            text = re.sub(r'\s+', ' ', text.strip())
        
        return text
    
    @staticmethod
    def _count_changes(diff_lines: List[str]) -> Tuple[int, int]:
        """Count additions and deletions from diff lines"""
        additions = 0
        deletions = 0
        
        for line in diff_lines:
            if line.startswith('+') and not line.startswith('+++'):
                additions += 1
            elif line.startswith('-') and not line.startswith('---'):
                deletions += 1
        
        return additions, deletions
    
    @classmethod
    async def generate_diff(cls, request: DiffRequest) -> DiffResponse:
        """Generate unified diff between two texts"""
        try:
            # Preprocess texts if needed
            original = cls._preprocess_text(
                request.original, 
                request.ignore_whitespace, 
                request.ignore_case
            )
            modified = cls._preprocess_text(
                request.modified, 
                request.ignore_whitespace, 
                request.ignore_case
            )
            
            # Split into lines
            original_lines = original.splitlines(keepends=True)
            modified_lines = modified.splitlines(keepends=True)
            
            # Generate unified diff
            diff_lines = list(difflib.unified_diff(
                original_lines,
                modified_lines,
                fromfile='original',
                tofile='modified',
                n=request.context_lines
            ))
            
            # Join diff lines
            diff_text = ''.join(diff_lines)
            
            # Count changes
            additions, deletions = cls._count_changes(diff_lines)
            has_changes = additions > 0 or deletions > 0
            
            # Calculate similarity ratio
            similarity_ratio = difflib.SequenceMatcher(
                None, 
                original_lines, 
                modified_lines
            ).ratio()
            
            logger.info(f"Generated diff: {additions} additions, {deletions} deletions")
            
            return DiffResponse(
                diff=diff_text,
                has_changes=has_changes,
                additions=additions,
                deletions=deletions,
                similarity_ratio=round(similarity_ratio, 4)
            )
            
        except Exception as e:
            logger.error(f"Error generating diff: {e}")
            raise
    
    @classmethod
    async def generate_detailed_diff(cls, request: DiffRequest) -> DetailedDiffResponse:
        """Generate detailed line-by-line diff"""
        try:
            # First get the summary
            summary = await cls.generate_diff(request)
            
            # Preprocess texts
            original = cls._preprocess_text(
                request.original, 
                request.ignore_whitespace, 
                request.ignore_case
            )
            modified = cls._preprocess_text(
                request.modified, 
                request.ignore_whitespace, 
                request.ignore_case
            )
            
            # Split into lines
            original_lines = original.splitlines()
            modified_lines = modified.splitlines()
            
            # Generate sequence matcher
            matcher = difflib.SequenceMatcher(None, original_lines, modified_lines)
            
            line_diffs = []
            line_number = 1
            
            for tag, i1, i2, j1, j2 in matcher.get_opcodes():
                if tag == 'equal':
                    # Unchanged lines
                    for i in range(i1, i2):
                        line_diffs.append(LineDiff(
                            line_number=line_number,
                            change_type='unchanged',
                            original_line=original_lines[i],
                            modified_line=original_lines[i]
                        ))
                        line_number += 1
                
                elif tag == 'delete':
                    # Deleted lines
                    for i in range(i1, i2):
                        line_diffs.append(LineDiff(
                            line_number=line_number,
                            change_type='deleted',
                            original_line=original_lines[i],
                            modified_line=None
                        ))
                        line_number += 1
                
                elif tag == 'insert':
                    # Added lines
                    for j in range(j1, j2):
                        line_diffs.append(LineDiff(
                            line_number=line_number,
                            change_type='added',
                            original_line=None,
                            modified_line=modified_lines[j]
                        ))
                        line_number += 1
                
                elif tag == 'replace':
                    # Modified lines
                    max_lines = max(i2 - i1, j2 - j1)
                    for k in range(max_lines):
                        original_line = original_lines[i1 + k] if i1 + k < i2 else None
                        modified_line = modified_lines[j1 + k] if j1 + k < j2 else None
                        
                        line_diffs.append(LineDiff(
                            line_number=line_number,
                            change_type='modified',
                            original_line=original_line,
                            modified_line=modified_line
                        ))
                        line_number += 1
            
            return DetailedDiffResponse(
                line_diffs=line_diffs,
                summary=summary
            )
            
        except Exception as e:
            logger.error(f"Error generating detailed diff: {e}")
            raise
    
    @classmethod
    async def generate_side_by_side_diff(cls, request: SideBySideDiffRequest) -> SideBySideDiffResponse:
        """Generate side-by-side diff comparison"""
        try:
            # Create a DiffRequest for summary
            diff_request = DiffRequest(
                original=request.original,
                modified=request.modified,
                ignore_whitespace=request.ignore_whitespace
            )
            summary = await cls.generate_diff(diff_request)
            
            # Preprocess texts
            original = cls._preprocess_text(
                request.original, 
                request.ignore_whitespace
            )
            modified = cls._preprocess_text(
                request.modified, 
                request.ignore_whitespace
            )
            
            # Split into lines
            original_lines = original.splitlines()
            modified_lines = modified.splitlines()
            
            # Generate side-by-side comparison
            lines = []
            line_number = 1
            
            # Use difflib to get the comparison
            matcher = difflib.SequenceMatcher(None, original_lines, modified_lines)
            
            for tag, i1, i2, j1, j2 in matcher.get_opcodes():
                if tag == 'equal':
                    # Unchanged lines
                    for i in range(i1, i2):
                        lines.append(SideBySideLine(
                            line_number=line_number,
                            original=original_lines[i],
                            modified=original_lines[i],
                            change_type='unchanged'
                        ))
                        line_number += 1
                
                elif tag == 'delete':
                    # Deleted lines
                    for i in range(i1, i2):
                        lines.append(SideBySideLine(
                            line_number=line_number,
                            original=original_lines[i],
                            modified=None,
                            change_type='deleted'
                        ))
                        line_number += 1
                
                elif tag == 'insert':
                    # Added lines
                    for j in range(j1, j2):
                        lines.append(SideBySideLine(
                            line_number=line_number,
                            original=None,
                            modified=modified_lines[j],
                            change_type='added'
                        ))
                        line_number += 1
                
                elif tag == 'replace':
                    # Modified lines
                    max_lines = max(i2 - i1, j2 - j1)
                    for k in range(max_lines):
                        original_line = original_lines[i1 + k] if i1 + k < i2 else None
                        modified_line = modified_lines[j1 + k] if j1 + k < j2 else None
                        
                        lines.append(SideBySideLine(
                            line_number=line_number,
                            original=original_line,
                            modified=modified_line,
                            change_type='modified'
                        ))
                        line_number += 1
            
            return SideBySideDiffResponse(
                lines=lines,
                summary=summary
            )
            
        except Exception as e:
            logger.error(f"Error generating side-by-side diff: {e}")
            raise
