#!/usr/bin/env python3
"""
Test the file upload functionality
"""
import asyncio
import httpx
import os

async def test_file_upload():
    """Test file upload and attachment functionality"""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing File Upload & Attachment")
    print("=" * 50)
    
    # Create a test file
    test_file_content = """# Test Python File
def hello_world():
    print("Hello from uploaded file!")
    return "success"

if __name__ == "__main__":
    hello_world()
"""
    
    test_file_path = "test_upload.py"
    with open(test_file_path, 'w') as f:
        f.write(test_file_content)
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print("📁 Uploading test file...")
            
            # Upload file
            with open(test_file_path, 'rb') as f:
                files = {'file': (test_file_path, f, 'text/x-python')}
                response = await client.post(f"{base_url}/api/files/upload", files=files)
            
            if response.status_code != 200:
                print(f"❌ Upload failed: {response.status_code}")
                print(f"Response: {response.text}")
                return
            
            upload_result = response.json()
            file_id = upload_result['id']
            
            print(f"✅ File uploaded successfully!")
            print(f"📄 File ID: {file_id}")
            print(f"📝 Filename: {upload_result['filename']}")
            print(f"📊 Size: {upload_result['size']} bytes")
            print(f"🔍 Hash: {upload_result['hash']}")
            
            # Test file retrieval
            print(f"\n📋 Testing file retrieval...")
            file_response = await client.get(f"{base_url}/api/files/{file_id}")
            
            if file_response.status_code != 200:
                print(f"❌ File retrieval failed: {file_response.status_code}")
                return
            
            file_data = file_response.json()
            print(f"✅ File retrieved successfully!")
            print(f"📄 Path: {file_data['file_path']}")
            print(f"📝 Content preview: {file_data.get('content', 'N/A')[:100]}...")
            
            # Test chat with attached file
            print(f"\n💬 Testing chat with attached file...")
            
            chat_response = await client.post(
                f"{base_url}/api/llm/query",
                headers={"Content-Type": "application/json"},
                json={
                    "prompt": "Please analyze the uploaded Python file and explain what it does.",
                    "model": "deepseek/deepseek-r1-0528:free",
                    "attached_files": [{
                        "id": file_id,
                        "filename": upload_result['filename'],
                        "size": upload_result['size'],
                        "mime_type": "text/x-python",
                        "hash": upload_result['hash']
                    }]
                }
            )
            
            if chat_response.status_code != 200:
                print(f"❌ Chat with file failed: {chat_response.status_code}")
                print(f"Response: {chat_response.text}")
                return
            
            chat_result = chat_response.json()
            print(f"✅ Chat with attached file successful!")
            print(f"🤖 AI Response: {chat_result['response'][:200]}...")
            print(f"🆔 Conversation ID: {chat_result['conversation_id']}")
            
            # Test file listing
            print(f"\n📋 Testing file listing...")
            list_response = await client.get(f"{base_url}/api/files")
            
            if list_response.status_code != 200:
                print(f"❌ File listing failed: {list_response.status_code}")
                return
            
            files_list = list_response.json()
            print(f"✅ File listing successful!")
            print(f"📊 Total files: {len(files_list)}")
            
            if files_list:
                latest_file = files_list[0]
                print(f"📄 Latest file: {latest_file['file_path']}")
                print(f"📅 Created: {latest_file['created_at']}")
            
            print(f"\n🎉 All file upload tests passed!")
            print("💡 Key features working:")
            print("  ✅ File upload with validation")
            print("  ✅ File storage in database")
            print("  ✅ File retrieval and metadata")
            print("  ✅ File attachment in chat")
            print("  ✅ AI analysis of attached files")
            print("  ✅ File listing and management")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
    
    finally:
        # Clean up test file
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

async def main():
    """Main test function"""
    print("🚀 Starting File Upload Test")
    print("Make sure the Flask app is running at http://localhost:5000")
    print("This test will upload a file and test attachment functionality.\n")
    
    await test_file_upload()
    
    print("\n✨ Test complete!")
    print("🔄 Try the file attachment in your browser!")

if __name__ == "__main__":
    asyncio.run(main())
