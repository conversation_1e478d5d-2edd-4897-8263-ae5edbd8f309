/**
 * Chat functionality for the Flask AI App
 */

class ChatManager {
    constructor() {
        this.conversationId = localStorage.getItem('currentConversationId');

        // Week 4 VITALLY IMPORTANT: Only generate new ID if no existing conversation
        if (!this.conversationId) {
            this.conversationId = `chat_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
            localStorage.setItem('currentConversationId', this.conversationId);
            console.log('🆕 Generated initial conversation ID:', this.conversationId);
        } else {
            console.log('🔄 Restored conversation ID:', this.conversationId);
        }
        this.attachedFiles = new Map(); // Store attached files
        // Week 4 VITALLY IMPORTANT: Context management
        this.contextStats = {
            total_tokens: 0,
            message_count: 0,
            context_health: 'excellent'
        };
        this.initializeElements();
        this.bindEvents();
        this.loadCurrentConversation();
        this.initializeTokenDisplay();
        // Week 4 VITALLY IMPORTANT: Load context stats for current conversation
        this.loadContextStats();
    }

    initializeElements() {
        // Chat elements
        this.chatForm = document.getElementById('chat-form');
        this.chatInput = document.getElementById('chat-input');
        this.sendButton = document.getElementById('send-button');
        this.chatMessages = document.getElementById('chat-messages');
        this.modelSelect = document.getElementById('model-select');
        this.attachFileButton = document.getElementById('attach-file-button');
        this.fileInput = document.getElementById('file-input');
        this.attachedFilesContainer = document.getElementById('attached-files');

        // Conversation management
        this.newConversationButton = document.getElementById('new-conversation-button');
        this.conversationsListButton = document.getElementById('conversations-list-button');
        this.conversationsModal = document.getElementById('conversations-modal');
        this.closeConversationsButton = document.getElementById('close-conversations-button');
        this.conversationsList = document.getElementById('conversations-list');

        // Week 4 VITALLY IMPORTANT: Token display elements
        this.tokenDisplay = document.getElementById('token-display');
        this.tokenCounter = document.getElementById('token-counter');
        this.tokenHealth = document.getElementById('token-health');
    }

    bindEvents() {
        // Chat form submission
        this.chatForm.addEventListener('submit', (e) => this.handleChatSubmit(e));

        // File attachment
        this.attachFileButton.addEventListener('click', () => this.fileInput.click());
        this.fileInput.addEventListener('change', (e) => this.handleFileAttachment(e));

        // Conversation management
        this.newConversationButton.addEventListener('click', () => this.startNewConversation());
        this.conversationsListButton.addEventListener('click', () => this.showConversationsList());
        this.closeConversationsButton.addEventListener('click', () => this.hideConversationsList());

        // Close modal when clicking outside
        this.conversationsModal.addEventListener('click', (e) => {
            if (e.target === this.conversationsModal) {
                this.hideConversationsList();
            }
        });

        // Handle Enter key in chat input (with Shift+Enter for new lines)
        this.chatInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.chatForm.dispatchEvent(new Event('submit'));
            }
        });
    }

    // Week 4 VITALLY IMPORTANT: Initialize token display
    initializeTokenDisplay() {
        // Create token display if it doesn't exist
        if (!this.tokenDisplay) {
            this.createTokenDisplayElement();
        }
        this.updateTokenDisplay();
    }

    createTokenDisplayElement() {
        // Find the chat header or create one
        const chatContainer = document.querySelector('.chat-container') || document.querySelector('#chat-messages').parentElement;

        // Create token display element
        const tokenDisplayHTML = `
            <div id="token-display" class="token-display bg-gray-100 border-b border-gray-200 px-4 py-2 text-sm">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <span class="font-medium text-gray-700">Context:</span>
                        <span id="token-counter" class="text-gray-600">0/160,000 tokens (0.0%)</span>
                        <span id="token-health" class="px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">🟢 Excellent</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-xs text-gray-500">DeepSeek R1 • 160k context</span>
                        <button id="compact-context-btn" class="text-xs text-blue-600 hover:text-blue-800 underline">🧠 Compact</button>
                    </div>
                </div>
            </div>
        `;

        // Insert before chat messages
        const chatMessages = document.getElementById('chat-messages');
        chatMessages.insertAdjacentHTML('beforebegin', tokenDisplayHTML);

        // Re-initialize elements
        this.tokenDisplay = document.getElementById('token-display');
        this.tokenCounter = document.getElementById('token-counter');
        this.tokenHealth = document.getElementById('token-health');

        // Add compact context button event
        document.getElementById('compact-context-btn').addEventListener('click', () => this.compactContext());
    }

    // Week 4 VITALLY IMPORTANT: Update token display
    updateTokenDisplay() {
        if (!this.tokenCounter || !this.tokenHealth) return;

        const { total_tokens, context_health } = this.contextStats;
        const maxTokens = 160000;
        const percentage = (total_tokens / maxTokens) * 100;

        // Update counter
        this.tokenCounter.textContent = `${total_tokens.toLocaleString()}/${maxTokens.toLocaleString()} tokens (${percentage.toFixed(1)}%)`;

        // Update health indicator
        const healthConfig = {
            'excellent': { icon: '🟢', text: 'Excellent', class: 'bg-green-100 text-green-800' },
            'good': { icon: '🟡', text: 'Good', class: 'bg-yellow-100 text-yellow-800' },
            'warning': { icon: '🟠', text: 'Warning', class: 'bg-orange-100 text-orange-800' },
            'critical': { icon: '🔴', text: 'Critical', class: 'bg-red-100 text-red-800' }
        };

        const config = healthConfig[context_health] || healthConfig['excellent'];
        this.tokenHealth.textContent = `${config.icon} ${config.text}`;
        this.tokenHealth.className = `px-2 py-1 rounded text-xs font-medium ${config.class}`;

        // Week 5 REVOLUTIONARY: Auto-compaction notification
        if (percentage >= 90 && context_health === 'critical') {
            this._showAutoCompactionSuggestion();
        }
    }

    // Week 5 REVOLUTIONARY: Show auto-compaction suggestion
    _showAutoCompactionSuggestion() {
        // Only show once per session to avoid spam
        if (this._autoCompactionSuggestionShown) return;
        this._autoCompactionSuggestionShown = true;

        // Show notification with compaction option
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-orange-100 border border-orange-300 text-orange-800 px-4 py-3 rounded-lg shadow-lg z-50 max-w-sm';
        notification.innerHTML = `
            <div class="flex items-start space-x-2">
                <div class="text-orange-600">🧠</div>
                <div class="flex-1">
                    <div class="font-medium text-sm">Context Nearly Full</div>
                    <div class="text-xs mt-1">Your conversation context is at 90%+ capacity. Consider compacting to maintain performance.</div>
                    <div class="flex space-x-2 mt-2">
                        <button id="auto-compact-btn" class="text-xs bg-orange-600 text-white px-2 py-1 rounded hover:bg-orange-700">
                            Compact Now
                        </button>
                        <button id="dismiss-compact-btn" class="text-xs text-orange-600 hover:text-orange-800">
                            Dismiss
                        </button>
                    </div>
                </div>
                <button class="text-orange-400 hover:text-orange-600" onclick="this.parentElement.parentElement.remove()">
                    ×
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // Add event listeners
        notification.querySelector('#auto-compact-btn').addEventListener('click', () => {
            notification.remove();
            this.compactContext();
        });

        notification.querySelector('#dismiss-compact-btn').addEventListener('click', () => {
            notification.remove();
        });

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 10000);
    }

    // Week 5 REVOLUTIONARY: Intelligent Context Compaction
    async compactContext() {
        if (confirm('Compact conversation context? This will intelligently summarize older messages while preserving recent conversation and important details.')) {
            try {
                // Show loading state
                const compactBtn = document.getElementById('compact-context-btn');
                const originalText = compactBtn.textContent;
                compactBtn.textContent = '🧠 Compacting...';
                compactBtn.disabled = true;

                const response = await fetch('/api/unified-ai/context-compact', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        conversation_id: this.conversationId
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // Update context stats
                    this.contextStats = {
                        total_tokens: result.stats.new_tokens,
                        message_count: result.stats.new_messages,
                        context_health: this._calculateContextHealth(result.stats.new_tokens)
                    };

                    // Update token display
                    this.updateTokenDisplay();

                    AppUtils.showNotification(result.message, 'success');
                } else {
                    AppUtils.showNotification(`Compaction failed: ${result.error}`, 'error');
                }

                // Restore button state
                compactBtn.textContent = originalText;
                compactBtn.disabled = false;

            } catch (error) {
                console.error('Error compacting context:', error);
                AppUtils.showNotification('Failed to compact context', 'error');

                // Restore button state
                const compactBtn = document.getElementById('compact-context-btn');
                compactBtn.textContent = '🧠 Compact';
                compactBtn.disabled = false;
            }
        }
    }

    // Helper method to calculate context health
    _calculateContextHealth(tokens) {
        const percentage = (tokens / 160000) * 100;

        if (percentage < 50) return 'excellent';
        if (percentage < 75) return 'good';
        if (percentage < 90) return 'warning';
        return 'critical';
    }

    // Week 4 VITALLY IMPORTANT: Load context stats for current conversation
    async loadContextStats() {
        if (!this.conversationId) return;

        try {
            const response = await AppUtils.apiRequest(`/api/unified-ai/context-stats?conversation_id=${this.conversationId}`);

            if (response.success && response.conversation_stats) {
                this.contextStats = {
                    total_tokens: response.conversation_stats.total_tokens || 0,
                    message_count: response.conversation_stats.message_count || 0,
                    context_health: response.conversation_stats.context_health || 'excellent'
                };

                // Ensure token display exists before updating
                if (!this.tokenDisplay) {
                    this.createTokenDisplayElement();
                }
                this.updateTokenDisplay();

                console.log('📊 Loaded context stats:', this.contextStats);
            }
        } catch (error) {
            console.log('⚠️ Could not load context stats (new conversation):', error.message);
            // This is normal for new conversations
        }
    }

    async handleChatSubmit(e) {
        e.preventDefault();
        
        const message = this.chatInput.value.trim();
        if (!message && this.attachedFiles.size === 0) return;

        // Add user message to chat
        if (message) {
            this.addMessage('user', message);
        }

        // Show attached files in chat if any
        if (this.attachedFiles.size > 0) {
            this.showAttachedFilesInChat();
        }

        // Clear input and disable send button
        this.chatInput.value = '';
        this.sendButton.disabled = true;
        this.sendButton.innerHTML = '<div class="spinner"></div>';

        try {
            // Show activity indicator instead of generic thinking message
            this.addActivityMessage();

            // Prepare context from attached files
            const context = {
                attached_files: Array.from(this.attachedFiles.values()),
                model: this.modelSelect.value,
                messages: this.messages
            };

            // Use unified AI agent with DeepSeek R1 for ALL requests
            const response = await AppUtils.apiRequest('/api/unified-ai/process', {
                method: 'POST',
                body: JSON.stringify({
                    prompt: message || "I've attached some files for you to analyze.",
                    conversation_id: this.conversationId,
                    context: context
                })
            });

            if (response && response.success) {
                // Replace the activity indicator with the actual response
                this.replaceActivityMessage(`🤖 **AI Assistant:**\n\n${response.response}`);

                // Update conversation ID if provided
                if (response.conversation_id) {
                    this.conversationId = response.conversation_id;
                    localStorage.setItem('currentConversationId', this.conversationId);
                }

                // Week 4 VITALLY IMPORTANT: Update context stats from response
                if (response.context_stats) {
                    this.contextStats = {
                        total_tokens: response.context_stats.total_tokens || 0,
                        message_count: response.context_stats.message_count || 0,
                        context_health: response.context_stats.context_health || 'excellent'
                    };

                    // Ensure token display exists before updating
                    if (!this.tokenDisplay) {
                        this.createTokenDisplayElement();
                    }
                    this.updateTokenDisplay();

                    console.log('🔄 Context stats updated:', this.contextStats);
                }

                // Clear attached files
                this.clearAttachedFiles();
            } else {
                // Handle error case
                this.removeActivityMessage();
                this.addMessage('system', `Error: ${response?.error || 'Unknown error occurred'}`);
            }


            
        } catch (error) {
            this.addMessage('system', `Error: ${error.message}`);
        } finally {
            this.sendButton.disabled = false;
            this.sendButton.innerHTML = 'Send';
        }
    }

    async handleFileAttachment(e) {
        const files = Array.from(e.target.files);
        if (files.length === 0) return;

        for (const file of files) {
            try {
                // Validate file
                if (!this.isValidFile(file)) {
                    AppUtils.showNotification(`Invalid file type: ${file.name}`, 'error');
                    continue;
                }

                // Upload file
                const uploadedFile = await this.uploadFile(file);
                
                // Add to attached files
                this.attachedFiles.set(uploadedFile.id, uploadedFile);
                
                // Update UI
                this.updateAttachedFilesUI();
                
                AppUtils.showNotification(`File attached: ${file.name}`, 'success');
                
            } catch (error) {
                AppUtils.showNotification(`Failed to attach ${file.name}: ${error.message}`, 'error');
            }
        }

        // Clear file input
        e.target.value = '';
    }

    async uploadFile(file) {
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch('/api/files/upload', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Upload failed');
        }

        return await response.json();
    }

    isValidFile(file) {
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = [
            'text/*',
            'application/json',
            'application/pdf',
            'image/*',
            'application/javascript',
            'text/x-python',
            'application/x-python-code'
        ];

        if (file.size > maxSize) {
            AppUtils.showNotification('File too large (max 10MB)', 'error');
            return false;
        }

        if (!AppUtils.isValidFileType(file, allowedTypes)) {
            AppUtils.showNotification('File type not supported', 'error');
            return false;
        }

        return true;
    }

    updateAttachedFilesUI() {
        if (this.attachedFiles.size === 0) {
            this.attachedFilesContainer.classList.add('hidden');
            return;
        }

        this.attachedFilesContainer.classList.remove('hidden');
        this.attachedFilesContainer.innerHTML = `
            <div class="text-xs text-gray-600 mb-2">Attached files:</div>
            <div class="flex flex-wrap gap-2">
                ${Array.from(this.attachedFiles.values()).map(file => `
                    <div class="flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                        <span>${file.filename}</span>
                        <button 
                            class="ml-2 text-blue-600 hover:text-blue-800"
                            onclick="chatManager.removeAttachedFile('${file.id}')"
                        >
                            ×
                        </button>
                    </div>
                `).join('')}
            </div>
        `;
    }

    removeAttachedFile(fileId) {
        this.attachedFiles.delete(fileId);
        this.updateAttachedFilesUI();
    }

    clearAttachedFiles() {
        this.attachedFiles.clear();
        this.updateAttachedFilesUI();
    }

    showAttachedFilesInChat() {
        const filesList = Array.from(this.attachedFiles.values())
            .map(file => `📎 ${file.filename} (${AppUtils.formatFileSize(file.size)})`)
            .join('\n');
        
        this.addMessage('system', `Attached files:\n${filesList}`);
    }

    addMessage(role, content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${
            role === 'user' ? 'ml-8' : 
            role === 'assistant' ? 'mr-8' : 
            'mx-4'
        }`;
        
        const roleColor = {
            'user': 'bg-blue-100 text-blue-800',
            'assistant': 'bg-green-100 text-green-800',
            'system': 'bg-gray-100 text-gray-800'
        }[role];
        
        messageDiv.innerHTML = `
            <div class="flex items-start space-x-2">
                <div class="px-2 py-1 rounded text-xs font-medium ${roleColor}">
                    ${role.charAt(0).toUpperCase() + role.slice(1)}
                </div>
                <div class="flex-1 bg-gray-50 rounded-lg p-3">
                    <pre class="whitespace-pre-wrap text-sm">${content}</pre>
                </div>
            </div>
        `;
        
        // Remove placeholder if it exists
        const placeholder = this.chatMessages.querySelector('.text-center');
        if (placeholder) {
            placeholder.remove();
        }
        
        this.chatMessages.appendChild(messageDiv);
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    addActivityMessage() {
        // Create a special activity message container
        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat-message mr-8 activity-message';
        messageDiv.setAttribute('data-activity-message', 'true');

        messageDiv.innerHTML = `
            <div class="flex items-start space-x-2">
                <div class="px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                    Assistant
                </div>
                <div class="flex-1 bg-gray-50 rounded-lg p-3">
                    <div class="chat-activity-container">
                        <div class="chat-activity-content">
                            <div class="chat-activity-icon">
                                <span id="chat-activity-emoji">🧠</span>
                            </div>
                            <div class="chat-activity-text">
                                <div id="chat-activity-title">Thinking</div>
                                <div id="chat-activity-description">Analyzing your request...</div>
                                <div class="chat-activity-progress">
                                    <div id="chat-activity-progress-bar" class="chat-activity-progress-bar">
                                        <div class="chat-activity-progress-shine"></div>
                                    </div>
                                    <span id="chat-activity-percentage">0%</span>
                                </div>
                            </div>
                        </div>
                        <div id="chat-activity-particles" class="chat-activity-particles"></div>
                    </div>
                </div>
            </div>
        `;

        // Remove placeholder if it exists
        const placeholder = this.chatMessages.querySelector('.text-center');
        if (placeholder) {
            placeholder.remove();
        }

        this.chatMessages.appendChild(messageDiv);
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;

        // Initialize the chat activity manager
        this.initializeChatActivityManager();
    }

    replaceActivityMessage(content) {
        const activityMessage = this.chatMessages.querySelector('[data-activity-message="true"]');
        if (activityMessage) {
            const messageContent = activityMessage.querySelector('.bg-gray-50');
            if (messageContent) {
                messageContent.innerHTML = `<pre class="whitespace-pre-wrap text-sm">${AppUtils.formatMessage(content)}</pre>`;
                activityMessage.removeAttribute('data-activity-message');
                activityMessage.classList.remove('activity-message');

                // Clean up the chat activity manager
                if (this.chatActivityManager) {
                    this.chatActivityManager.cleanup();
                }
            }
        } else {
            // Fallback: add as new message
            this.addMessage('assistant', content);
        }
    }

    removeActivityMessage() {
        const activityMessage = this.chatMessages.querySelector('[data-activity-message="true"]');
        if (activityMessage) {
            activityMessage.remove();

            // Clean up the chat activity manager
            if (this.chatActivityManager) {
                this.chatActivityManager.cleanup();
            }
        }
    }

    initializeChatActivityManager() {
        // Create a chat-specific activity manager
        if (!this.chatActivityManager) {
            this.chatActivityManager = new ChatActivityManager();
        }
    }

    async streamWithReasoning(context) {
        // Stream LLM response with real-time reasoning content for DeepSeek R1
        try {
            const response = await fetch('/api/llm/stream-reasoning', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(context)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let reasoningBuffer = '';
            let contentBuffer = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));

                            if (data.type === 'reasoning') {
                                reasoningBuffer += data.content;

                                // Update activity with reasoning snippet
                                const reasoningSnippet = reasoningBuffer.slice(-100).trim();
                                this.updateChatActivityDescription(`🧠 Reasoning: ${reasoningSnippet}...`);
                                this.updateChatActivityProgress(data.progress);

                            } else if (data.type === 'content') {
                                contentBuffer += data.content;

                                // Switch to writing mode
                                this.updateChatActivityTitle('Writing');
                                this.updateChatActivityDescription('Crafting response...');
                                this.updateChatActivityProgress(data.progress);

                            } else if (data.type === 'complete') {
                                // Replace activity with final response
                                const finalResponse = contentBuffer || reasoningBuffer;
                                this.replaceActivityMessage(`🤖 **AI Assistant:**\n\n${finalResponse}`);
                                break;

                            } else if (data.type === 'error') {
                                this.removeActivityMessage();
                                this.addMessage('system', `Error: ${data.message}`);
                                break;
                            }
                        } catch (e) {
                            console.error('Error parsing stream data:', e);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error in streamWithReasoning:', error);
            this.removeActivityMessage();
            this.addMessage('system', `Error: ${error.message}`);
        }
    }

    updateChatActivityTitle(title) {
        const titleElement = document.getElementById('chat-activity-title');
        if (titleElement) {
            titleElement.textContent = title;
        }
    }

    updateChatActivityDescription(description) {
        const descElement = document.getElementById('chat-activity-description');
        if (descElement) {
            descElement.textContent = description;
        }
    }

    updateChatActivityProgress(progress) {
        const progressBar = document.getElementById('chat-activity-progress-bar');
        const percentage = document.getElementById('chat-activity-percentage');

        if (progressBar && percentage) {
            const progressPercent = Math.round(progress * 100);
            progressBar.style.width = `${progressPercent}%`;
            percentage.textContent = `${progressPercent}%`;
        }
    }

    startNewConversation() {
        // Week 4 VITALLY IMPORTANT: Generate unique conversation ID for new chat
        this.conversationId = `chat_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        localStorage.setItem('currentConversationId', this.conversationId);
        console.log('🆕 Started new conversation:', this.conversationId);

        // Clear chat messages
        this.chatMessages.innerHTML = `
            <div class="text-center text-gray-500 py-8">
                <p>Start a conversation with the AI!</p>
            </div>
        `;

        // Clear attached files
        this.clearAttachedFiles();

        // Week 4 VITALLY IMPORTANT: Reset context stats
        this.contextStats = {
            total_tokens: 0,
            message_count: 0,
            context_health: 'excellent'
        };
        this.updateTokenDisplay();

        AppUtils.showNotification('Started new conversation', 'success');
    }

    showConversationsList() {
        this.conversationsModal.classList.remove('hidden');
        this.loadConversationsList();
    }

    hideConversationsList() {
        this.conversationsModal.classList.add('hidden');
    }

    async loadConversationsList() {
        try {
            this.conversationsList.innerHTML = '<div class="text-center text-gray-500 py-4">Loading...</div>';

            const response = await AppUtils.apiRequest('/api/llm/conversations');
            const conversations = response.conversations;

            if (conversations.length === 0) {
                this.conversationsList.innerHTML = `
                    <div class="text-center text-gray-500 py-8">
                        <p>No conversations yet</p>
                        <p class="text-sm">Start chatting to create your first conversation!</p>
                    </div>
                `;
                return;
            }

            this.conversationsList.innerHTML = '';

            conversations.forEach(conv => {
                const convDiv = document.createElement('div');
                convDiv.className = 'border rounded-lg p-3 hover:bg-gray-50 cursor-pointer';

                const isActive = conv.conversation_id === this.conversationId;
                if (isActive) {
                    convDiv.classList.add('bg-blue-50', 'border-blue-200');
                }

                convDiv.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="text-sm font-medium text-gray-900">
                                ${isActive ? '🔵 ' : ''}Conversation ${conv.conversation_id.substring(0, 8)}...
                            </div>
                            <div class="text-xs text-gray-500">
                                ${conv.message_count} messages • ${conv.total_tokens} tokens
                                ${conv.total_cost > 0 ? ` • $${conv.total_cost.toFixed(4)}` : ''}
                            </div>
                            <div class="text-xs text-gray-400">
                                ${new Date(conv.last_message_at).toLocaleString()}
                            </div>
                        </div>
                        <div class="flex space-x-1">
                            <button
                                class="load-conversation-btn px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                                data-conversation-id="${conv.conversation_id}"
                            >
                                Load
                            </button>
                            <button
                                class="delete-conversation-btn px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
                                data-conversation-id="${conv.conversation_id}"
                            >
                                Delete
                            </button>
                        </div>
                    </div>
                `;

                this.conversationsList.appendChild(convDiv);
            });

            // Add event listeners for load and delete buttons
            this.conversationsList.querySelectorAll('.load-conversation-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const convId = btn.dataset.conversationId;
                    this.loadConversationHistory(convId);
                });
            });

            this.conversationsList.querySelectorAll('.delete-conversation-btn').forEach(btn => {
                btn.addEventListener('click', async (e) => {
                    e.stopPropagation();
                    const convId = btn.dataset.conversationId;

                    if (confirm('Are you sure you want to delete this conversation?')) {
                        try {
                            await AppUtils.apiRequest(`/api/llm/conversations/${convId}`, {
                                method: 'DELETE'
                            });

                            // If this was the current conversation, start a new one
                            if (convId === this.conversationId) {
                                this.startNewConversation();
                            }

                            // Reload the conversations list
                            this.loadConversationsList();

                            AppUtils.showNotification('Conversation deleted', 'success');
                        } catch (error) {
                            AppUtils.showNotification('Failed to delete conversation', 'error');
                        }
                    }
                });
            });

        } catch (error) {
            this.conversationsList.innerHTML = `
                <div class="text-center text-red-500 py-8">
                    <p>Failed to load conversations</p>
                </div>
            `;
        }
    }

    async loadConversationHistory(convId) {
        try {
            const messages = await AppUtils.apiRequest(`/api/llm/conversations/${convId}`);

            // Clear current messages
            this.chatMessages.innerHTML = '';

            // Add all messages from history
            messages.forEach(msg => {
                this.addMessage(msg.role, msg.content);
            });

            // Set current conversation ID
            this.conversationId = convId;
            localStorage.setItem('currentConversationId', this.conversationId);

            // Close modal
            this.hideConversationsList();

            AppUtils.showNotification('Conversation loaded', 'success');

            // Week 4 VITALLY IMPORTANT: Reload context stats after conversation is loaded
            await this.loadContextStats();

        } catch (error) {
            AppUtils.showNotification('Failed to load conversation', 'error');
        }
    }

    async loadCurrentConversation() {
        if (this.conversationId) {
            try {
                const messages = await AppUtils.apiRequest(`/api/llm/conversations/${this.conversationId}`);

                if (messages.length > 0) {
                    // Clear placeholder
                    this.chatMessages.innerHTML = '';

                    // Add all messages from history
                    messages.forEach(msg => {
                        this.addMessage(msg.role, msg.content);
                    });

                    AppUtils.showNotification('Previous conversation restored', 'info');

                    // Week 4 VITALLY IMPORTANT: Reload context stats after conversation is restored
                    await this.loadContextStats();
                }
            } catch (error) {
                // If conversation doesn't exist anymore, start fresh
                this.startNewConversation();
            }
        }
    }

    // Removed hardcoded routing - now the LLM intelligently decides which tools to use!
}

// Chat Activity Manager - handles activity animations within chat messages
class ChatActivityManager {
    constructor() {
        this.isActive = false;
        this.currentActivity = null;
        this.animationInterval = null;

        // Activity type configurations
        this.animations = {
            thinking: { icon: '🧠', color: '#6366f1', text: 'Thinking', animation: 'pulse' },
            analyzing: { icon: '🔍', color: '#8b5cf6', text: 'Analyzing', animation: 'scan' },
            coding: { icon: '💻', color: '#10b981', text: 'Coding', animation: 'type' },
            writing: { icon: '✍️', color: '#f59e0b', text: 'Writing', animation: 'write' },
            reading: { icon: '📖', color: '#3b82f6', text: 'Reading', animation: 'flip' },
            modifying: { icon: '🔧', color: '#ef4444', text: 'Modifying', animation: 'spin' },
            creating: { icon: '✨', color: '#ec4899', text: 'Creating', animation: 'sparkle' },
            searching: { icon: '🔎', color: '#06b6d4', text: 'Searching', animation: 'search' },
            processing: { icon: '⚡', color: '#84cc16', text: 'Processing', animation: 'flash' },
            planning: { icon: '📋', color: '#6366f1', text: 'Planning', animation: 'organize' }
        };

        this.setupEventListeners();
    }

    setupEventListeners() {
        // Listen for activity events from the global activity stream
        if (window.eventSource) {
            window.eventSource.addEventListener('ai_activity', (event) => {
                try {
                    const activityData = JSON.parse(event.data);
                    this.handleActivityEvent(activityData);
                } catch (error) {
                    console.error('❌ Error parsing chat activity event:', error);
                }
            });
        }

        // Also listen for polling updates
        this.startPolling();
    }

    startPolling() {
        // Only poll when there's an active chat activity message
        this.pollingInterval = setInterval(async () => {
            // Check if there's an active activity message in chat
            const activityMessage = document.querySelector('[data-activity-message="true"]');
            if (!activityMessage) {
                return; // No active activity message, skip polling
            }

            try {
                const response = await fetch('/api/activity/current');
                const data = await response.json();

                if (data.active && (!this.currentActivity ||
                    this.currentActivity.activity_type !== data.activity_type ||
                    this.currentActivity.description !== data.description ||
                    Math.abs(this.currentActivity.progress - data.progress) > 0.01)) {

                    this.handleActivityEvent(data);

                } else if (!data.active && this.currentActivity) {
                    this.handleActivityEvent({ type: 'activity_ended' });
                }

            } catch (error) {
                // Silently handle polling errors
            }
        }, 300); // Reduced frequency to 300ms
    }

    handleActivityEvent(activityData) {
        if (activityData.type === 'activity_ended') {
            this.endActivity();
            return;
        }

        const activityType = activityData.activity_type;
        const description = activityData.description;
        const progress = activityData.progress || 0;

        // Update current activity
        this.currentActivity = activityData;
        this.isActive = true;

        // Update the chat activity display
        this.updateChatActivity(activityType, description, progress);
    }

    updateChatActivity(activityType, description, progress) {
        const emoji = document.getElementById('chat-activity-emoji');
        const title = document.getElementById('chat-activity-title');
        const desc = document.getElementById('chat-activity-description');
        const progressBar = document.getElementById('chat-activity-progress-bar');
        const percentage = document.getElementById('chat-activity-percentage');

        if (!emoji || !title || !desc || !progressBar || !percentage) {
            return; // Elements not found
        }

        const config = this.animations[activityType] || this.animations.processing;

        // Update content
        emoji.textContent = config.icon;
        title.textContent = config.text;
        desc.textContent = description;
        percentage.textContent = `${Math.round(progress * 100)}%`;

        // Update progress bar
        const progressPercent = Math.max(0, Math.min(100, progress * 100));
        progressBar.style.width = `${progressPercent}%`;
        progressBar.style.backgroundColor = config.color;

        // Add animation class
        const container = document.querySelector('.chat-activity-container');
        if (container) {
            container.className = `chat-activity-container chat-activity-${config.animation}`;
            container.style.borderColor = config.color;
        }
    }

    endActivity() {
        this.isActive = false;
        this.currentActivity = null;

        // The activity message will be replaced by the actual response
        // No need to do anything here as the ChatManager handles the replacement
    }

    cleanup() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
        this.endActivity();
    }
}

// Initialize chat manager when DOM is loaded
let chatManager;
document.addEventListener('DOMContentLoaded', () => {
    chatManager = new ChatManager();
});
