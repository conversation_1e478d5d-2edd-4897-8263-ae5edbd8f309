#!/usr/bin/env python3
"""
Test the AI Coder functionality with DeepSeek workaround
"""
import asyncio
import httpx
import time

async def test_ai_coder_functionality():
    """Test AI Coder with file operations and DeepSeek compatibility"""
    base_url = "http://localhost:5000"
    
    print("🤖 Testing AI Coder with DeepSeek Workaround")
    print("=" * 60)
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            
            # Test 1: File Creation
            print("📝 Test 1: AI Coder File Creation")
            print("-" * 40)
            
            response1 = await client.post(
                f"{base_url}/api/ai-coder/process",
                headers={"Content-Type": "application/json"},
                json={
                    "prompt": """Create a new Python file called 'hello_ai.py' with a simple function that prints a greeting message. Use the XML tool format to create the file.

<tool_call>
<tool>file_operation</tool>
<parameters>{"operation": "create", "file_path": "hello_ai.py", "content": "#!/usr/bin/env python3\\n\\ndef greet_ai():\\n    print('Hello from AI Coder!')\\n    return 'AI is working!'\\n\\nif __name__ == '__main__':\\n    result = greet_ai()\\n    print(f'Result: {result}')"}</parameters>
</tool_call>""",
                    "context": "This is a test of the AI Coder functionality"
                }
            )
            
            if response1.status_code == 200:
                result1 = response1.json()
                print(f"✅ AI Coder Response: {result1['response'][:200]}...")
            else:
                print(f"❌ Error: {response1.status_code}")
                print(f"Response: {response1.text}")
                return
            
            await asyncio.sleep(2)
            
            # Test 2: File Reading
            print("\n📖 Test 2: AI Coder File Reading")
            print("-" * 40)
            
            response2 = await client.post(
                f"{base_url}/api/ai-coder/process",
                headers={"Content-Type": "application/json"},
                json={
                    "prompt": """Read the hello_ai.py file I just created and tell me what it does.

<tool_call>
<tool>file_operation</tool>
<parameters>{"operation": "read", "file_path": "hello_ai.py"}</parameters>
</tool_call>""",
                    "context": "Analyze the Python file"
                }
            )
            
            if response2.status_code == 200:
                result2 = response2.json()
                print(f"✅ AI Analysis: {result2['response'][:200]}...")
            else:
                print(f"❌ Error: {response2.status_code}")
                return
            
            await asyncio.sleep(2)
            
            # Test 3: File Listing
            print("\n📋 Test 3: AI Coder File Listing")
            print("-" * 40)
            
            response3 = await client.post(
                f"{base_url}/api/ai-coder/process",
                headers={"Content-Type": "application/json"},
                json={
                    "prompt": """List all Python files in the current directory.

<tool_call>
<tool>file_operation</tool>
<parameters>{"operation": "list", "file_path": "."}</parameters>
</tool_call>""",
                    "context": "Show me the project structure"
                }
            )
            
            if response3.status_code == 200:
                result3 = response3.json()
                print(f"✅ File Listing: {result3['response'][:200]}...")
            else:
                print(f"❌ Error: {response3.status_code}")
                return
            
            await asyncio.sleep(2)
            
            # Test 4: Direct File Operation API
            print("\n🔧 Test 4: Direct File Operation API")
            print("-" * 40)
            
            response4 = await client.post(
                f"{base_url}/api/ai-coder/file-operation",
                headers={"Content-Type": "application/json"},
                json={
                    "operation": "read",
                    "file_path": "hello_ai.py"
                }
            )
            
            if response4.status_code == 200:
                result4 = response4.json()
                print(f"✅ Direct File Read Success: {result4['success']}")
                print(f"📄 File Content: {result4['content'][:100]}...")
                print(f"📊 File Size: {result4['file_size']} bytes")
            else:
                print(f"❌ Error: {response4.status_code}")
                return
            
            # Test 5: Workspace Listing
            print("\n🗂️ Test 5: Workspace Listing")
            print("-" * 40)
            
            response5 = await client.get(f"{base_url}/api/ai-coder/list-workspace")
            
            if response5.status_code == 200:
                result5 = response5.json()
                print(f"✅ Workspace Listing Success: {result5['success']}")
                if result5['content']:
                    import json
                    files = json.loads(result5['content'])
                    print(f"📁 Found {len(files)} items in workspace")
                    for file in files[:5]:  # Show first 5 files
                        icon = "📁" if file['is_dir'] else "📄"
                        size = f" ({file['size']} bytes)" if file['is_file'] else ""
                        print(f"  {icon} {file['name']}{size}")
            else:
                print(f"❌ Error: {response5.status_code}")
                return
            
            print(f"\n🎉 All AI Coder tests passed!")
            print("💡 Key features working:")
            print("  ✅ AI Coder with DeepSeek XML tool calling workaround")
            print("  ✅ File creation through AI instructions")
            print("  ✅ File reading and analysis")
            print("  ✅ Directory listing and file management")
            print("  ✅ Direct file operation API")
            print("  ✅ Workspace file listing")
            print("  ✅ Secure file path validation")
            print("  ✅ Multiple file format support")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")

async def test_chat_integration():
    """Test chat integration with AI Coder"""
    base_url = "http://localhost:5000"
    
    print("\n💬 Testing Chat Integration with AI Coder")
    print("=" * 50)
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            
            # Test coding request through chat
            response = await client.post(
                f"{base_url}/api/llm/query",
                headers={"Content-Type": "application/json"},
                json={
                    "prompt": "Create a simple Python function that calculates the factorial of a number",
                    "model": "deepseek/deepseek-r1-0528:free"
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Chat Integration Success!")
                print(f"🤖 AI Response: {result['response'][:200]}...")
                print(f"🆔 Conversation ID: {result['conversation_id']}")
            else:
                print(f"❌ Chat Integration Error: {response.status_code}")
                
    except Exception as e:
        print(f"❌ Error during chat test: {e}")

async def main():
    """Main test function"""
    print("🚀 Starting Comprehensive AI Coder Test")
    print("Make sure the Flask app is running at http://localhost:5000")
    print("This test will verify all AI coding functionality.\n")
    
    await test_ai_coder_functionality()
    await test_chat_integration()
    
    print("\n✨ All tests complete!")
    print("🌟 Your Flask AI App now has BRILLIANT AI coding capabilities!")
    print("🔗 Try the Files page at http://localhost:5000/files")

if __name__ == "__main__":
    asyncio.run(main())
