#!/usr/bin/env python3
"""
Test the layout to ensure footer doesn't overlap content
"""
import asyncio
import httpx

async def test_layout():
    """Test that the layout is working correctly"""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing Layout Fix")
    print("=" * 30)
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            print("📄 Fetching homepage...")
            
            response = await client.get(base_url)
            
            if response.status_code == 200:
                html_content = response.text
                
                # Check for key layout elements
                checks = [
                    ("flex-1 grid grid-cols-1", "✅ Main grid uses flex-1"),
                    ("h-screen flex flex-col", "✅ Body uses full screen height"),
                    ("flex-shrink-0", "✅ Fixed elements use flex-shrink-0"),
                    ("overflow-y-auto", "✅ Chat messages are scrollable"),
                    ("min-h-0", "✅ Proper height constraints applied")
                ]
                
                print("\n🔍 Layout Structure Check:")
                for check_text, success_msg in checks:
                    if check_text in html_content:
                        print(f"  {success_msg}")
                    else:
                        print(f"  ❌ Missing: {check_text}")
                
                print(f"\n📊 Page loaded successfully ({len(html_content)} bytes)")
                print("🎯 Layout should now have:")
                print("  • Fixed navigation at top")
                print("  • Scrollable chat content in middle")
                print("  • Fixed footer at bottom (no overlap)")
                print("  • Grid content fits perfectly between nav and footer")
                
            else:
                print(f"❌ Error: {response.status_code}")
                
    except Exception as e:
        print(f"❌ Error during test: {e}")

async def main():
    """Main test function"""
    print("🚀 Testing Layout Fix")
    print("Make sure to refresh your browser at http://localhost:5000\n")
    
    await test_layout()
    
    print("\n✨ Test complete!")
    print("💡 The footer should now be properly positioned at the bottom")
    print("🔧 The grid content should fit perfectly in the available space")

if __name__ == "__main__":
    asyncio.run(main())
