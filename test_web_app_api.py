#!/usr/bin/env python3
"""
Test the web app API directly to see what happens
"""

import asyncio
import httpx
import json

async def test_web_app_api():
    """Test the web app API with the failing prompt"""
    print("🌐 **Testing Web App API**\n")
    
    try:
        async with httpx.AsyncClient(timeout=120.0) as client:
            prompt = "Create a very creative website about plants, including a blog system"
            print(f"🎯 **Testing Prompt:** {prompt}")
            print("-" * 60)
            
            # Test the unified AI endpoint
            response = await client.post(
                'http://localhost:5000/api/unified-ai/process',
                headers={'Content-Type': 'application/json'},
                json={
                    "prompt": prompt,
                    "conversation_id": None,
                    "context": {}
                }
            )
            
            print(f"📊 **Status Code:** {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ **Success:** {data.get('success', False)}")
                print(f"📋 **Response:**")
                print(data.get('response', 'No response'))
                
                if 'context_stats' in data:
                    stats = data['context_stats']
                    print(f"\n📊 **Context Stats:**")
                    print(f"   Tokens: {stats.get('total_tokens', 0)}")
                    print(f"   Messages: {stats.get('message_count', 0)}")
                    print(f"   Health: {stats.get('context_health', 'unknown')}")
                    
            else:
                print(f"❌ **Error:** {response.status_code}")
                print(response.text)
                
    except Exception as e:
        print(f"❌ **Exception:** {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_web_app_api())
