#!/usr/bin/env python3
"""
Test just the chat interface
"""
import asyncio
import httpx

async def test_chat():
    """Test the chat interface"""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print("💬 Testing Chat Interface...")
            
            response = await client.post(
                'http://localhost:5000/api/llm/query',
                headers={'Content-Type': 'application/json'},
                json={
                    "prompt": "Hello! How are you?",
                    "messages": [{"role": "user", "content": "Hello! How are you?"}],
                    "model": "deepseek/deepseek-r1-0528:free",
                    "conversation_id": None,
                    "attached_files": []
                }
            )
            
            print(f"Status: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_chat())
