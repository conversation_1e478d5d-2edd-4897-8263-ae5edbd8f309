#!/usr/bin/env python3
"""
Test script for TRULY DYNAMIC AI-Powered Workflow Planning
Tests the full UnifiedAIAgent with AI-generated workflow plans
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.unified_agent import get_unified_agent

async def test_full_ai_planning():
    """Test the full AI-powered planning through UnifiedAIAgent"""
    print("🤖 **TESTING FULL AI-POWERED DYNAMIC PLANNING**\n")
    
    try:
        agent = get_unified_agent()
        
        test_cases = [
            "Plan this: Create a contact form with validation",
            "Create a portfolio website for a photographer",
            "Build a simple calculator app"
        ]
        
        for prompt in test_cases:
            print(f"🎯 **Testing:** {prompt}")
            print("-" * 60)
            
            try:
                # This should trigger the AI-powered workflow planning
                result = await agent.process_request(prompt)
                print(result)
                
            except Exception as e:
                print(f"❌ Error: {e}")
            
            print("\n" + "=" * 60 + "\n")
            
    except Exception as e:
        print(f"❌ Failed to initialize agent: {e}")

if __name__ == "__main__":
    asyncio.run(test_full_ai_planning())
