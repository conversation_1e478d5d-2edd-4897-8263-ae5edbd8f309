<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="A blog dedicated to all things <PERSON><PERSON><PERSON><PERSON>, including care tips, training advice, fun stories, and more.">
    <title><PERSON>hn<PERSON><PERSON> Snippets - Home</title>
    <style>
        /* CSS Variables */
        :root {
            --primary: #4a6fa5;
            --secondary: #d4c1a3;
            --accent: #6b5b45;
            --light: #f8f5f0;
            --dark: #333;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 2rem;
            --spacing-lg: 3rem;
            --border-radius: 8px;
            --shadow: 0 4px 6px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }

        /* Reset & Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--dark);
            background-color: var(--light);
        }

        img {
            max-width: 100%;
            height: auto;
            display: block;
        }

        a {
            text-decoration: none;
            color: var(--primary);
        }

        /* Layout */
        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-sm);
        }

        /* Header */
        header {
            background-color: white;
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm) 0;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .logo-icon {
            font-size: 2rem;
        }

        .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
        }

        /* Navigation */
        nav ul {
            display: flex;
            list-style: none;
            gap: var(--spacing-md);
        }

        nav a {
            font-weight: 600;
            padding: var(--spacing-xs) 0;
            position: relative;
        }

        nav a:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: var(--accent);
            transition: var(--transition);
        }

        nav a:hover:after,
        nav a.active:after {
            width: 100%;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(rgba(74, 111, 165, 0.8), rgba(74, 111, 165, 0.9)), url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%234a6fa5"/><path d="M0 0L100 100M100 0L0 100" stroke="%23d4c1a3" stroke-width="2"/></svg>');
            background-size: cover;
            color: white;
            text-align: center;
            padding: var(--spacing-lg) 0;
            margin-bottom: var(--spacing-lg);
        }

        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: var(--spacing-sm);
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .hero p {
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto var(--spacing-md);
        }

        .btn {
            display: inline-block;
            background-color: var(--secondary);
            color: var(--dark);
            padding: 0.8rem 1.5rem;
            border-radius: var(--border-radius);
            font-weight: 600;
            transition: var(--transition);
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            background-color: var(--accent);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        /* Main Content */
        main {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
            padding-bottom: var(--spacing-lg);
        }

        @media (min-width: 768px) {
            main {
                grid-template-columns: 2fr 1fr;
            }
        }

        /* Articles */
        .posts h2 {
            color: var(--primary);
            padding-bottom: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
            border-bottom: 2px solid var(--secondary);
        }

        .article-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: var(--spacing-md);
        }

        .article {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: var(--spacing-sm);
        }

        .article h3 {
            margin-bottom: var(--spacing-xs);
        }

        .article p {
            margin-bottom: var(--spacing-sm);
        }

        .footer {
            text-align: center;
            padding: var(--spacing-lg) 0;
            background-color: var(--secondary);
            color: var(--dark);
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="logo">
                <span class="logo-icon">🐶</span>
                <span class="logo-text">Schnauzer Snippets</span>
            </div>
            <nav>
                <ul>
                    <li><a href="home.html" class="active">Home</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="blog.html">Blog</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </nav>
        </div>
    </header>
    <div class="hero">
        <h1>Welcome to Schnauzer Snippets!</h1>
        <p>Your go-to blog for all things Schnauzer.</p>
        <a href="#posts" class="btn">Explore Our Posts</a>
    </div>
    <main>
        <section class="posts" id="posts">
            <h2>Latest Posts</h2>
            <div class="article-grid">
                <div class="article">
                    <h3>Post Title 1</h3>
                    <p>Summary of post 1. This is a brief overview...</p>
                </div>
                <div class="article">
                    <h3>Post Title 2</h3>
                    <p>Summary of post 2. An interesting fact about schnauzers...</p>
                </div>
                <div class="article">
                    <h3>Post Title 3</h3>
                    <p>Summary of post 3. Tips on caring for your schnauzer...</p>
                </div>
            </div>
        </section>
    </main>
    <footer class="footer">
        <p>&copy; 2023 Schnauzer Snippets. All rights reserved.</p>
    </footer>
</body>
</html>
