/**
 * Files Manager for the Flask AI App
 */

class FilesManager {
    constructor() {
        this.currentFile = null;
        this.fileTree = new Map();
        this.initializeElements();
        this.bindEvents();
        this.loadFileTree();
    }

    initializeElements() {
        // Main buttons
        this.createFileBtn = document.getElementById('create-file-btn');
        this.refreshFilesBtn = document.getElementById('refresh-files-btn');

        // File tree
        this.fileTreeContainer = document.getElementById('file-tree');

        // File editor
        this.fileContentEditor = document.getElementById('file-content-editor');
        this.currentFileDisplay = document.getElementById('current-file-display');
        this.saveCurrentFileBtn = document.getElementById('save-current-file-btn');
        this.deleteCurrentFileBtn = document.getElementById('delete-current-file-btn');
        this.fileEditorStatus = document.getElementById('file-editor-status');

        // File info panel
        this.fileInfoPanel = document.getElementById('file-info-panel');
        this.fileIcon = document.getElementById('file-icon');
        this.fileName = document.getElementById('file-name');
        this.fileDetails = document.getElementById('file-details');
        this.downloadFileBtn = document.getElementById('download-file-btn');

        // Create file modal
        this.createFileModal = document.getElementById('create-file-modal');
        this.newFilePath = document.getElementById('new-file-path');
        this.fileTemplateSelect = document.getElementById('file-template-select');
        this.cancelCreateFile = document.getElementById('cancel-create-file');
        this.confirmCreateFile = document.getElementById('confirm-create-file');
    }

    bindEvents() {
        // Main buttons
        this.createFileBtn.addEventListener('click', () => this.showCreateFileModal());
        this.refreshFilesBtn.addEventListener('click', () => this.loadFileTree());

        // File editor
        this.saveCurrentFileBtn.addEventListener('click', () => this.saveCurrentFile());
        this.deleteCurrentFileBtn.addEventListener('click', () => this.deleteCurrentFile());
        this.fileContentEditor.addEventListener('input', () => this.handleEditorChange());

        // Create file modal
        this.cancelCreateFile.addEventListener('click', () => this.hideCreateFileModal());
        this.confirmCreateFile.addEventListener('click', () => this.createNewFile());
        this.newFilePath.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.createNewFile();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));

        // Close modals when clicking outside
        this.createFileModal.addEventListener('click', (e) => {
            if (e.target === this.createFileModal) this.hideCreateFileModal();
        });
    }

    async loadFileTree() {
        try {
            this.fileTreeContainer.innerHTML = '<div class="text-center text-gray-500 py-4">Loading...</div>';

            const response = await AppUtils.apiRequest('/api/ai-coder/list-workspace');
            
            if (response.success) {
                const files = JSON.parse(response.content);
                this.renderFileTree(files);
            } else {
                this.fileTreeContainer.innerHTML = `
                    <div class="text-center text-red-500 py-4">
                        <p>Failed to load files</p>
                        <p class="text-sm">${response.error || response.message}</p>
                    </div>
                `;
            }
        } catch (error) {
            this.fileTreeContainer.innerHTML = `
                <div class="text-center text-red-500 py-4">
                    <p>Error loading files</p>
                    <p class="text-sm">${error.message}</p>
                </div>
            `;
        }
    }

    renderFileTree(files) {
        if (files.length === 0) {
            this.fileTreeContainer.innerHTML = `
                <div class="text-center text-gray-500 py-8">
                    <p>No files found</p>
                    <p class="text-sm">Create your first file to get started!</p>
                </div>
            `;
            return;
        }

        this.fileTreeContainer.innerHTML = '';

        files.forEach(file => {
            const fileElement = document.createElement('div');
            fileElement.className = 'flex items-center space-x-2 p-2 hover:bg-gray-100 rounded cursor-pointer';
            
            const icon = file.is_dir ? '📁' : this.getFileIcon(file.extension);
            const isActive = this.currentFile === file.path;
            
            if (isActive) {
                fileElement.classList.add('bg-blue-100', 'border-blue-200');
            }

            fileElement.innerHTML = `
                <span class="text-lg">${icon}</span>
                <span class="flex-1 text-sm ${isActive ? 'font-medium text-blue-800' : 'text-gray-700'}">${file.name}</span>
                <span class="text-xs text-gray-500">${file.is_file ? AppUtils.formatFileSize(file.size) : ''}</span>
            `;

            if (file.is_file) {
                fileElement.addEventListener('click', () => this.openFile(file.path));
            }

            this.fileTreeContainer.appendChild(fileElement);
        });
    }

    getFileIcon(extension) {
        const icons = {
            '.py': '🐍',
            '.js': '📜',
            '.html': '🌐',
            '.css': '🎨',
            '.json': '📋',
            '.md': '📝',
            '.txt': '📄',
            '.yaml': '⚙️',
            '.yml': '⚙️',
            '.sql': '🗃️',
            '.png': '🖼️',
            '.jpg': '🖼️',
            '.jpeg': '🖼️',
            '.gif': '🖼️',
            '.svg': '🖼️',
            '.pdf': '📕'
        };
        return icons[extension] || '📄';
    }

    async openFile(filePath) {
        try {
            this.fileEditorStatus.textContent = 'Loading...';

            const response = await AppUtils.apiRequest('/api/ai-coder/file-operation', {
                method: 'POST',
                body: JSON.stringify({
                    operation: 'read',
                    file_path: filePath
                })
            });

            if (response.success) {
                this.currentFile = filePath;
                this.currentFileDisplay.textContent = filePath;
                
                // Check if file is text-based
                if (this.isTextFile(filePath)) {
                    this.fileContentEditor.value = response.content || '';
                    this.fileContentEditor.classList.remove('hidden');
                    this.fileInfoPanel.classList.add('hidden');
                    this.saveCurrentFileBtn.disabled = false;
                    this.deleteCurrentFileBtn.disabled = false;
                } else {
                    // Show file info panel for non-text files
                    this.fileContentEditor.classList.add('hidden');
                    this.fileInfoPanel.classList.remove('hidden');
                    this.fileName.textContent = filePath.split('/').pop();
                    this.fileDetails.textContent = `Size: ${AppUtils.formatFileSize(response.file_size || 0)}`;
                    this.fileIcon.textContent = this.getFileIcon(AppUtils.getFileExtension(filePath));
                    this.saveCurrentFileBtn.disabled = true;
                    this.deleteCurrentFileBtn.disabled = false;
                }

                this.fileEditorStatus.textContent = `Loaded (${AppUtils.formatFileSize(response.file_size || 0)})`;
                this.loadFileTree(); // Refresh to show active file
                
            } else {
                this.fileEditorStatus.textContent = 'Error loading file';
                AppUtils.showNotification(`Failed to load file: ${response.error || response.message}`, 'error');
            }

        } catch (error) {
            this.fileEditorStatus.textContent = 'Error loading file';
            AppUtils.showNotification(`Failed to load file: ${error.message}`, 'error');
        }
    }

    isTextFile(filePath) {
        const textExtensions = ['.py', '.js', '.html', '.css', '.json', '.md', '.txt', '.yaml', '.yml', '.sql', '.xml', '.csv', '.tsv'];
        const extension = AppUtils.getFileExtension(filePath);
        return textExtensions.includes('.' + extension);
    }

    async saveCurrentFile() {
        // If no file is selected, prompt for file name
        if (!this.currentFile) {
            const content = this.fileContentEditor.value.trim();
            if (!content) {
                AppUtils.showNotification('Please enter some content before saving', 'warning');
                return;
            }

            const fileName = prompt('Enter file name (e.g., my-script.py):');
            if (!fileName) return;

            try {
                this.fileEditorStatus.textContent = 'Creating...';

                const response = await AppUtils.apiRequest('/api/ai-coder/file-operation', {
                    method: 'POST',
                    body: JSON.stringify({
                        operation: 'create',
                        file_path: fileName,
                        content: content
                    })
                });

                if (response.success) {
                    this.currentFile = fileName;
                    this.currentFileDisplay.textContent = fileName;
                    this.deleteCurrentFileBtn.disabled = false;
                    this.fileEditorStatus.textContent = `Created (${AppUtils.formatFileSize(response.file_size || 0)})`;
                    AppUtils.showNotification(`File created: ${fileName}`, 'success');
                    this.loadFileTree(); // Refresh file tree
                } else {
                    this.fileEditorStatus.textContent = 'Error creating file';
                    AppUtils.showNotification(`Failed to create file: ${response.error || response.message}`, 'error');
                }

            } catch (error) {
                this.fileEditorStatus.textContent = 'Error creating file';
                AppUtils.showNotification(`Failed to create file: ${error.message}`, 'error');
            }
            return;
        }

        try {
            this.fileEditorStatus.textContent = 'Saving...';

            const response = await AppUtils.apiRequest('/api/ai-coder/file-operation', {
                method: 'POST',
                body: JSON.stringify({
                    operation: 'write',
                    file_path: this.currentFile,
                    content: this.fileContentEditor.value
                })
            });

            if (response.success) {
                this.fileEditorStatus.textContent = `Saved (${AppUtils.formatFileSize(response.file_size || 0)})`;
                AppUtils.showNotification('File saved successfully!', 'success');
                this.loadFileTree(); // Refresh file tree
            } else {
                this.fileEditorStatus.textContent = 'Error saving file';
                AppUtils.showNotification(`Failed to save file: ${response.error || response.message}`, 'error');
            }

        } catch (error) {
            this.fileEditorStatus.textContent = 'Error saving file';
            AppUtils.showNotification(`Failed to save file: ${error.message}`, 'error');
        }
    }

    async deleteCurrentFile() {
        if (!this.currentFile) return;

        if (!confirm(`Are you sure you want to delete "${this.currentFile}"?`)) return;

        try {
            const response = await AppUtils.apiRequest('/api/ai-coder/file-operation', {
                method: 'POST',
                body: JSON.stringify({
                    operation: 'delete',
                    file_path: this.currentFile
                })
            });

            if (response.success) {
                AppUtils.showNotification('File deleted successfully!', 'success');
                this.clearEditor();
                this.loadFileTree(); // Refresh file tree
            } else {
                AppUtils.showNotification(`Failed to delete file: ${response.error || response.message}`, 'error');
            }

        } catch (error) {
            AppUtils.showNotification(`Failed to delete file: ${error.message}`, 'error');
        }
    }

    clearEditor() {
        this.currentFile = null;
        this.currentFileDisplay.textContent = 'No file selected - start typing to create a new file';
        this.fileContentEditor.value = '';
        this.fileContentEditor.classList.remove('hidden');
        this.fileInfoPanel.classList.add('hidden');
        this.saveCurrentFileBtn.disabled = true;
        this.deleteCurrentFileBtn.disabled = true;
        this.fileEditorStatus.textContent = 'Ready';
    }

    handleEditorChange() {
        const hasContent = this.fileContentEditor.value.trim().length > 0;

        if (this.currentFile) {
            this.fileEditorStatus.textContent = 'Modified';
        } else if (hasContent) {
            this.fileEditorStatus.textContent = 'Ready to save';
            this.saveCurrentFileBtn.disabled = false;
        } else {
            this.fileEditorStatus.textContent = 'Ready';
            this.saveCurrentFileBtn.disabled = true;
        }
    }

    handleKeyboardShortcuts(e) {
        // Ctrl+S for saving
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            if (!this.saveCurrentFileBtn.disabled) {
                this.saveCurrentFile();
            }
        }

        // Ctrl+N for new file
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            this.showCreateFileModal();
        }
    }

    showCreateFileModal() {
        this.createFileModal.classList.remove('hidden');
        this.newFilePath.focus();
    }

    hideCreateFileModal() {
        this.createFileModal.classList.add('hidden');
        this.newFilePath.value = '';
        this.fileTemplateSelect.value = 'empty';
    }



    async createNewFile() {
        const filePath = this.newFilePath.value.trim();
        if (!filePath) {
            AppUtils.showNotification('Please enter a file path', 'error');
            return;
        }

        try {
            const template = this.fileTemplateSelect.value;
            const content = this.getFileTemplate(template, filePath);

            const response = await AppUtils.apiRequest('/api/ai-coder/file-operation', {
                method: 'POST',
                body: JSON.stringify({
                    operation: 'create',
                    file_path: filePath,
                    content: content
                })
            });

            if (response.success) {
                AppUtils.showNotification(`File created: ${filePath}`, 'success');
                this.hideCreateFileModal();
                this.loadFileTree();
                // Open the newly created file
                setTimeout(() => this.openFile(filePath), 500);
            } else {
                AppUtils.showNotification(`Failed to create file: ${response.error || response.message}`, 'error');
            }

        } catch (error) {
            AppUtils.showNotification(`Failed to create file: ${error.message}`, 'error');
        }
    }

    getFileTemplate(template, filePath) {
        const fileName = filePath.split('/').pop();
        const baseName = fileName.split('.')[0];

        const templates = {
            'empty': '',
            'python': `#!/usr/bin/env python3
"""
${fileName} - Python script
"""

def main():
    """Main function"""
    print("Hello from ${fileName}!")

if __name__ == "__main__":
    main()
`,
            'javascript': `/**
 * ${fileName} - JavaScript module
 */

// Your code here
console.log("Hello from ${fileName}!");
`,
            'html': `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${baseName}</title>
</head>
<body>
    <h1>Hello from ${fileName}!</h1>
</body>
</html>
`,
            'css': `/* ${fileName} - Stylesheet */

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
}
`,
            'json': `{
    "name": "${baseName}",
    "version": "1.0.0",
    "description": "Generated from ${fileName}"
}
`,
            'markdown': `# ${baseName}

This is a new markdown document.

## Getting Started

Add your content here...
`,
            'yaml': `# ${fileName} - YAML configuration
name: ${baseName}
version: 1.0.0
description: Generated configuration file
`
        };

        return templates[template] || '';
    }


}

// Initialize files manager when DOM is loaded
let filesManager;
document.addEventListener('DOMContentLoaded', () => {
    filesManager = new FilesManager();
});
