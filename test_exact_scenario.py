#!/usr/bin/env python3
"""
Test the exact scenario that's failing
"""

import sys
import os
import asyncio
import logging

# Set up logging to see everything
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.unified_agent import get_unified_agent

async def test_exact_scenario():
    """Test the exact failing scenario"""
    print("🔧 **TESTING EXACT FAILING SCENARIO**")
    
    try:
        agent = get_unified_agent()
        print("✅ Agent initialized")
        
        # Use the exact prompt that's failing
        prompt = "Hey! Please create a very creative website, ensure you plan it first"
        print(f"🎯 **Testing:** {prompt}")
        
        # Call process_request which is what the web app calls
        result = await agent.process_request(prompt)
        
        print("📋 **RESULT:**")
        print(result)
        
        # Check for the specific error
        if "'str' object has no attribute 'file_path'" in result:
            print("\n❌ **FOUND THE BUG!**")
            print("The error is happening in the workflow execution")
            return False
        else:
            print("\n✅ **NO BUG FOUND!**")
            return True
        
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_exact_scenario())
