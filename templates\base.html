<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Flask AI App{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Activity Animation Styles -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/activity-animations.css') }}">

    <!-- Custom styles -->
    <style>
        .code-editor {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
        
        .chat-message {
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .diff-added {
            background-color: #d4edda;
            color: #155724;
        }
        
        .diff-removed {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .diff-modified {
            background-color: #fff3cd;
            color: #856404;
        }

        /* Smooth scrolling for chat messages */
        #chat-messages {
            scroll-behavior: smooth;
        }

        /* Custom scrollbar for chat messages */
        #chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        #chat-messages::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        #chat-messages::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        #chat-messages::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Ensure proper height constraints */
        .h-screen-minus-nav {
            height: calc(100vh - 4rem); /* Subtract nav height */
        }

        /* Prevent any potential overflow issues */
        html, body {
            height: 100%;
            overflow-x: hidden;
        }

        /* Ensure main content area uses available space properly */
        main {
            overflow: hidden;
        }
    </style>
</head>
<body class="bg-gray-100 h-screen flex flex-col">
    <!-- Navigation: Fixed height -->
    <nav class="bg-white shadow-sm border-b flex-shrink-0">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center space-x-4">
                    <h1 class="text-xl font-semibold text-gray-900">
                        <a href="/" class="hover:text-blue-600">🤖 AI Coder</a>
                    </h1>
                    <div class="text-sm text-gray-500" id="current-project-display">
                        <span id="project-name-nav">No Project</span>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <button
                        id="new-project-button"
                        class="bg-purple-600 text-white px-3 py-1 rounded-md text-sm font-medium hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                    >
                        📁 New Project
                    </button>
                    <button
                        id="switch-project-button"
                        class="bg-indigo-600 text-white px-3 py-1 rounded-md text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    >
                        🔄 Projects
                    </button>
                    <a href="/" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        Chat
                    </a>
                    <a href="/files" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        Files
                    </a>
                    <a href="/jobs" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        Jobs
                    </a>
                    <a href="/tools" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        Tools
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main content: Takes remaining space -->
    <main class="flex-1 max-w-7xl mx-auto w-full min-h-0 flex flex-col">
        {% block content %}{% endblock %}
    </main>

    <!-- Activity Animation Container -->
    <div id="activity-container" class="activity-container hidden">
        <div class="activity-content">
            <div class="activity-icon">
                <span id="activity-emoji">🧠</span>
            </div>
            <div class="activity-text">
                <div id="activity-title">Thinking</div>
                <div id="activity-description">Analyzing your request...</div>
                <div class="activity-progress">
                    <div id="activity-progress-bar" class="activity-progress-bar">
                        <div class="activity-progress-shine"></div>
                    </div>
                    <span id="activity-percentage">0%</span>
                </div>
            </div>
        </div>
        <div id="activity-particles" class="activity-particles"></div>
    </div>

    <!-- Test Button for Activity Animations (Development Only) -->
    <div style="position: fixed; top: 10px; left: 10px; z-index: 10000;">
        <button id="test-activity-btn" onclick="testActivityAnimation()"
                style="background: #6366f1; color: white; border: none; padding: 8px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">
            🧪 Test Activity
        </button>
    </div>

    <!-- Footer: Fixed height -->
    <footer class="bg-white border-t flex-shrink-0">
        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <p class="text-center text-sm text-gray-500">
                Flask AI App - Built with Flask, SQLite, and OpenRouter
            </p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Global utilities
        window.AppUtils = {
            // Show notification
            showNotification: function(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${
                    type === 'error' ? 'bg-red-500 text-white' :
                    type === 'success' ? 'bg-green-500 text-white' :
                    type === 'warning' ? 'bg-yellow-500 text-black' :
                    'bg-blue-500 text-white'
                }`;
                notification.textContent = message;
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.remove();
                }, 5000);
            },
            
            // Format timestamp
            formatTimestamp: function(timestamp) {
                return new Date(timestamp).toLocaleString();
            },
            
            // Copy to clipboard
            copyToClipboard: function(text) {
                navigator.clipboard.writeText(text).then(() => {
                    this.showNotification('Copied to clipboard!', 'success');
                }).catch(() => {
                    this.showNotification('Failed to copy to clipboard', 'error');
                });
            },
            
            // API request helper
            apiRequest: async function(url, options = {}) {
                const defaultOptions = {
                    headers: {
                        'Content-Type': 'application/json',
                    },
                };

                const mergedOptions = { ...defaultOptions, ...options };

                try {
                    const response = await fetch(url, mergedOptions);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return await response.json();
                } catch (error) {
                    this.showNotification(`API Error: ${error.message}`, 'error');
                    throw error;
                }
            },

            // Format message for display (converts markdown-like syntax to HTML)
            formatMessage: function(message) {
                if (!message) return '';

                // Convert markdown-like formatting to HTML
                let formatted = message
                    // Bold text: **text** -> <strong>text</strong>
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    // Code blocks: ```code``` -> <pre><code>code</code></pre>
                    .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
                    // Inline code: `code` -> <code>code</code>
                    .replace(/`([^`]+)`/g, '<code>$1</code>')
                    // Line breaks
                    .replace(/\n/g, '<br>');

                return formatted;
            }
        };

        // Auto-resize textareas
        document.addEventListener('DOMContentLoaded', function() {
            const textareas = document.querySelectorAll('textarea[data-auto-resize]');
            textareas.forEach(textarea => {
                textarea.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = this.scrollHeight + 'px';
                });
            });
        });
    </script>

    <!-- Project Management -->
    <script src="{{ url_for('static', filename='js/project-manager.js') }}"></script>

    <!-- Activity Animation Manager -->
    <script src="{{ url_for('static', filename='js/activity-animations.js') }}"></script>

    <!-- Initialize Activity Stream Connection -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎬 Initializing activity stream connection...');

            // Connect to activity stream
            if (typeof EventSource !== 'undefined') {
                window.eventSource = new EventSource('/activity-stream');

                window.eventSource.onopen = function() {
                    console.log('🎬 Activity stream connected successfully');
                };

                window.eventSource.onerror = function(error) {
                    console.error('❌ Activity stream error:', error);
                    console.log('🔄 Activity stream will attempt to reconnect...');
                };

                window.eventSource.onmessage = function(event) {
                    console.log('📨 Received activity stream message:', event.data);
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'connected') {
                            console.log('✅ Activity stream ready');
                        } else if (data.type === 'heartbeat') {
                            console.log('💓 Activity stream heartbeat');
                        } else {
                            console.log('📊 Activity stream data:', data);
                        }
                    } catch (error) {
                        console.error('❌ Error parsing activity stream message:', error);
                    }
                };

                // Listen for specific activity events
                window.eventSource.addEventListener('ai_activity', function(event) {
                    console.log('🎯 AI Activity event received:', event.data);
                    try {
                        const activityData = JSON.parse(event.data);
                        console.log('🎯 Parsed activity data:', activityData);

                        // Test the animation manager
                        if (window.activityAnimationManager) {
                            window.activityAnimationManager.handleActivityEvent(activityData);
                        } else {
                            console.error('❌ Activity animation manager not found');
                        }
                    } catch (error) {
                        console.error('❌ Error handling AI activity event:', error);
                    }
                });

                // Clean up on page unload
                window.addEventListener('beforeunload', function() {
                    if (window.eventSource) {
                        console.log('🔌 Closing activity stream connection');
                        window.eventSource.close();
                    }
                });
            } else {
                console.warn('⚠️ EventSource not supported - activity animations disabled');
            }
        });

        // Test function for activity animations
        window.testActivityAnimation = function() {
            console.log('🧪 Manual test triggered');

            if (window.activityAnimationManager) {
                const testActivities = [
                    { activity_type: 'thinking', description: 'Testing thinking animation', progress: 0.3 },
                    { activity_type: 'coding', description: 'Testing coding animation', progress: 0.6 },
                    { activity_type: 'analyzing', description: 'Testing analyzing animation', progress: 0.8 },
                    { activity_type: 'creating', description: 'Testing creating animation', progress: 1.0 }
                ];

                let currentIndex = 0;

                function runNextTest() {
                    if (currentIndex < testActivities.length) {
                        const activity = testActivities[currentIndex];
                        console.log('🧪 Testing activity:', activity);
                        window.activityAnimationManager.handleActivityEvent(activity);
                        currentIndex++;
                        setTimeout(runNextTest, 2000);
                    } else {
                        // End the test
                        setTimeout(() => {
                            window.activityAnimationManager.handleActivityEvent({ type: 'activity_ended' });
                        }, 2000);
                    }
                }

                runNextTest();
            } else {
                console.error('❌ Activity animation manager not found');
                alert('Activity animation manager not loaded yet. Please wait a moment and try again.');
            }
        };
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
