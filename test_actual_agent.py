#!/usr/bin/env python3
"""
Test the actual UnifiedAIAgent with the failing prompt
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.unified_agent import get_unified_agent

async def test_actual_agent():
    """Test the actual agent with the failing prompt"""
    print("🔍 **Testing Actual UnifiedAIAgent**\n")
    
    try:
        agent = get_unified_agent()
        print("✅ Agent initialized successfully")
        
        prompt = "Create a very creative website about plants, including a blog system"
        print(f"🎯 **Testing Prompt:** {prompt}")
        print("-" * 60)
        
        # This should trigger workflow planning
        result = await agent.process_request(prompt)
        
        print("📋 **RESULT:**")
        print(result)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_actual_agent())
