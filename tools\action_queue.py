#!/usr/bin/env python3
"""
Phase 2: Action Queue System
Multi-action workflow execution with progress tracking
"""

import asyncio
import logging
import time
from dataclasses import dataclass
from enum import Enum
from typing import List, Optional, Dict, Any, AsyncGenerator
from pathlib import Path
import json

logger = logging.getLogger(__name__)

class ActionType(Enum):
    """Types of actions that can be executed in workflows"""
    CREATE_FILE = "create_file"
    MODIFY_FILE = "modify_file"
    READ_FILE = "read_file"
    ANALYZE = "analyze"
    SUMMARY = "summary"

class ActionStatus(Enum):
    """Status of individual actions"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

@dataclass
class WorkflowAction:
    """Individual action in a workflow"""
    id: str
    action_type: ActionType
    description: str
    file_path: Optional[str] = None
    content: Optional[str] = None
    estimated_time: Optional[str] = None
    status: ActionStatus = ActionStatus.PENDING
    result: Optional[str] = None
    error: Optional[str] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None

@dataclass
class ActionResult:
    """Result of executing an action"""
    action_id: str
    action_type: ActionType
    status: ActionStatus
    result: str
    file_path: Optional[str] = None
    execution_time: Optional[float] = None
    error: Optional[str] = None

class ActionQueue:
    """Manages and executes workflow actions sequentially"""
    
    def __init__(self, workspace_root: Path, project_manager=None):
        self.workspace_root = workspace_root
        self.project_manager = project_manager
        self.actions: List[WorkflowAction] = []
        self.current_action_index = 0
        self.is_executing = False
        self.execution_id = None
        
    def add_action(self, action: WorkflowAction):
        """Add an action to the queue"""
        self.actions.append(action)
        logger.info(f"📋 Added action to queue: {action.description}")
    
    def clear_queue(self):
        """Clear all actions from the queue"""
        self.actions.clear()
        self.current_action_index = 0
        self.is_executing = False
        logger.info("🗑️ Action queue cleared")
    
    def get_queue_status(self) -> Dict[str, Any]:
        """Get current status of the action queue"""
        total_actions = len(self.actions)
        completed_actions = sum(1 for action in self.actions if action.status == ActionStatus.COMPLETED)
        failed_actions = sum(1 for action in self.actions if action.status == ActionStatus.FAILED)
        
        return {
            "total_actions": total_actions,
            "completed_actions": completed_actions,
            "failed_actions": failed_actions,
            "current_action_index": self.current_action_index,
            "is_executing": self.is_executing,
            "progress_percentage": (completed_actions / total_actions * 100) if total_actions > 0 else 0
        }
    
    async def execute_workflow(self, workflow_plan) -> AsyncGenerator[ActionResult, None]:
        """Execute all actions in the workflow plan sequentially"""
        if self.is_executing:
            raise RuntimeError("Workflow is already executing")
        
        self.is_executing = True
        self.execution_id = f"workflow_{int(time.time())}"
        
        try:
            # Convert workflow plan steps to actions
            self._convert_plan_to_actions(workflow_plan)
            
            logger.info(f"🚀 Starting workflow execution: {len(self.actions)} actions")
            
            # Execute each action sequentially
            for i, action in enumerate(self.actions):
                self.current_action_index = i
                
                logger.info(f"⚡ Executing action {i+1}/{len(self.actions)}: {action.description}")
                
                # Execute the action
                result = await self._execute_action(action)
                
                # Yield the result for streaming
                yield result
                
                # Stop execution if action failed (optional - could continue)
                if result.status == ActionStatus.FAILED:
                    logger.error(f"❌ Action failed: {action.description}")
                    # Could implement retry logic here
                    break
            
            logger.info("✅ Workflow execution completed")
            
        except Exception as e:
            logger.error(f"❌ Workflow execution failed: {e}")
            raise
        finally:
            self.is_executing = False
    
    def _convert_plan_to_actions(self, workflow_plan):
        """Convert workflow plan steps to executable actions"""
        self.clear_queue()

        logger.info(f"🔍 Converting workflow plan with {len(workflow_plan.steps)} steps")

        for i, step in enumerate(workflow_plan.steps):
            action_id = f"action_{i+1}"

            # Debug: Log step details
            logger.info(f"📋 Step {i+1}: {type(step)} - {step}")

            # Handle case where step might be a string or invalid object
            if isinstance(step, str):
                logger.error(f"❌ Step {i+1} is a string, not a WorkflowStep object: {step}")
                continue

            if not hasattr(step, 'action_type'):
                logger.error(f"❌ Step {i+1} missing action_type: {step}")
                continue

            if not hasattr(step, 'description'):
                logger.error(f"❌ Step {i+1} missing description: {step}")
                continue

            # Determine action type from step
            action_type = self._determine_action_type(step.action_type)

            # Handle missing file_path for create_file actions
            file_path = getattr(step, 'file_path', None)
            if action_type == ActionType.CREATE_FILE and not file_path:
                # Generate a default file path based on description
                file_path = self._generate_default_file_path(step.description)
                logger.warning(f"⚠️ Generated default file path for step {i+1}: {file_path}")

            action = WorkflowAction(
                id=action_id,
                action_type=action_type,
                description=step.description,
                file_path=file_path,
                estimated_time=getattr(step, 'estimated_time', None)
            )

            self.add_action(action)

    def _generate_default_file_path(self, description: str) -> str:
        """Generate a default file path based on the description"""
        desc_lower = description.lower()

        # Common patterns for file type detection
        if any(word in desc_lower for word in ['html', 'webpage', 'page', 'homepage']):
            if 'contact' in desc_lower:
                return 'contact.html'
            elif 'about' in desc_lower:
                return 'about.html'
            else:
                return 'index.html'
        elif any(word in desc_lower for word in ['css', 'style', 'styling']):
            return 'styles.css'
        elif any(word in desc_lower for word in ['javascript', 'js', 'script', 'interactive']):
            return 'script.js'
        elif any(word in desc_lower for word in ['python', 'py', 'app']):
            return 'main.py'
        elif any(word in desc_lower for word in ['readme', 'documentation', 'docs']):
            return 'README.md'
        else:
            # Default fallback
            return 'generated_file.txt'

    def _determine_action_type(self, step_action_type: str) -> ActionType:
        """Convert step action type to ActionType enum"""
        mapping = {
            "create_file": ActionType.CREATE_FILE,
            "modify_file": ActionType.MODIFY_FILE,
            "read_file": ActionType.READ_FILE,
            "analyze": ActionType.ANALYZE,
            "summary": ActionType.SUMMARY
        }
        return mapping.get(step_action_type, ActionType.CREATE_FILE)
    
    async def _execute_action(self, action: WorkflowAction) -> ActionResult:
        """Execute a single action"""
        action.status = ActionStatus.IN_PROGRESS
        action.start_time = time.time()
        
        try:
            if action.action_type == ActionType.CREATE_FILE:
                result = await self._execute_create_file(action)
            elif action.action_type == ActionType.MODIFY_FILE:
                result = await self._execute_modify_file(action)
            elif action.action_type == ActionType.READ_FILE:
                result = await self._execute_read_file(action)
            elif action.action_type == ActionType.ANALYZE:
                result = await self._execute_analyze(action)
            elif action.action_type == ActionType.SUMMARY:
                result = await self._execute_summary(action)
            else:
                raise ValueError(f"Unknown action type: {action.action_type}")
            
            action.status = ActionStatus.COMPLETED
            action.result = result
            action.end_time = time.time()
            
            execution_time = action.end_time - action.start_time
            
            return ActionResult(
                action_id=action.id,
                action_type=action.action_type,
                status=ActionStatus.COMPLETED,
                result=result,
                file_path=action.file_path,
                execution_time=execution_time
            )
            
        except Exception as e:
            action.status = ActionStatus.FAILED
            action.error = str(e)
            action.end_time = time.time()
            
            logger.error(f"❌ Action execution failed: {e}")
            
            return ActionResult(
                action_id=action.id,
                action_type=action.action_type,
                status=ActionStatus.FAILED,
                result=f"Action failed: {str(e)}",
                file_path=action.file_path,
                error=str(e)
            )
    
    async def _execute_create_file(self, action: WorkflowAction) -> str:
        """Execute file creation action"""
        if not action.file_path:
            raise ValueError("File path is required for create_file action")
        
        # Generate content using AI based on the action description
        content = await self._generate_file_content(action)
        
        # Create the file
        full_path = self.workspace_root / action.file_path
        
        # Create parent directories if needed
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Write the file
        full_path.write_text(content, encoding='utf-8')
        
        # Update project file count if project manager available
        if self.project_manager:
            self.project_manager.update_project_file_count()
        
        file_size = len(content.encode('utf-8'))
        
        return f"✅ Created file '{action.file_path}' ({file_size} bytes)\n📝 {action.description}"
    
    async def _execute_modify_file(self, action: WorkflowAction) -> str:
        """Execute file modification action"""
        if not action.file_path:
            raise ValueError("File path is required for modify_file action")
        
        full_path = self.workspace_root / action.file_path
        
        if not full_path.exists():
            raise FileNotFoundError(f"File '{action.file_path}' does not exist")
        
        # Read current content
        current_content = full_path.read_text(encoding='utf-8')
        
        # Generate modifications using AI
        modified_content = await self._generate_file_modifications(action, current_content)
        
        # Write modified content
        full_path.write_text(modified_content, encoding='utf-8')
        
        return f"✅ Modified file '{action.file_path}'\n📝 {action.description}"
    
    async def _execute_read_file(self, action: WorkflowAction) -> str:
        """Execute file reading action"""
        if not action.file_path:
            raise ValueError("File path is required for read_file action")
        
        full_path = self.workspace_root / action.file_path
        
        if not full_path.exists():
            raise FileNotFoundError(f"File '{action.file_path}' does not exist")
        
        content = full_path.read_text(encoding='utf-8')
        file_size = len(content.encode('utf-8'))
        
        return f"📄 Read file '{action.file_path}' ({file_size} bytes)\n```\n{content}\n```"
    
    async def _execute_analyze(self, action: WorkflowAction) -> str:
        """Execute analysis action"""
        # Simulate analysis work
        await asyncio.sleep(1)  # Simulate processing time
        
        return f"🔍 Analysis complete: {action.description}"
    
    async def _execute_summary(self, action: WorkflowAction) -> str:
        """Execute summary action"""
        completed_files = [a.file_path for a in self.actions if a.status == ActionStatus.COMPLETED and a.file_path]
        
        summary = f"🎉 Workflow Complete!\n\n"
        summary += f"📊 Created {len(completed_files)} files:\n"
        
        for file_path in completed_files:
            summary += f"• {file_path}\n"
        
        summary += f"\n✨ {action.description}"
        
        return summary
    
    async def _generate_file_content(self, action: WorkflowAction) -> str:
        """Generate file content using AI based on action description"""
        # This will be enhanced to use the AI agent for content generation
        # For now, return placeholder content based on file type
        
        if not action.file_path:
            return "# Generated content\n\nContent generated based on workflow action."
        
        file_ext = action.file_path.split('.')[-1].lower()
        
        if file_ext == 'html':
            return self._generate_html_content(action)
        elif file_ext == 'css':
            return self._generate_css_content(action)
        elif file_ext == 'js':
            return self._generate_js_content(action)
        elif file_ext == 'py':
            return self._generate_python_content(action)
        else:
            return f"# {action.description}\n\nGenerated content for {action.file_path}"
    
    def _generate_html_content(self, action: WorkflowAction) -> str:
        """Generate HTML content"""
        return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Page</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <h1>Welcome</h1>
    </header>
    
    <main>
        <section>
            <h2>Content Section</h2>
            <p>This content was generated based on: {action.description}</p>
        </section>
    </main>
    
    <footer>
        <p>&copy; 2024 Generated Website</p>
    </footer>
    
    <script src="script.js"></script>
</body>
</html>"""
    
    def _generate_css_content(self, action: WorkflowAction) -> str:
        """Generate CSS content"""
        return f"""/* Generated CSS - {action.description} */

* {{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}}

body {{
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
}}

header {{
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    text-align: center;
}}

main {{
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}}

section {{
    background: white;
    padding: 2rem;
    margin: 1rem 0;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}}

footer {{
    background: #333;
    color: white;
    text-align: center;
    padding: 1rem 0;
    margin-top: 2rem;
}}

/* Responsive design */
@media (max-width: 768px) {{
    main {{
        padding: 0 0.5rem;
    }}
    
    section {{
        padding: 1rem;
    }}
}}"""
    
    def _generate_js_content(self, action: WorkflowAction) -> str:
        """Generate JavaScript content"""
        return f"""// Generated JavaScript - {action.description}

document.addEventListener('DOMContentLoaded', function() {{
    console.log('Page loaded successfully');
    
    // Add interactive functionality
    initializeInteractivity();
}});

function initializeInteractivity() {{
    // Add click handlers
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {{
        button.addEventListener('click', handleButtonClick);
    }});
    
    // Add smooth scrolling
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {{
        link.addEventListener('click', handleSmoothScroll);
    }});
}}

function handleButtonClick(event) {{
    console.log('Button clicked:', event.target);
    // Add button functionality here
}}

function handleSmoothScroll(event) {{
    event.preventDefault();
    const target = document.querySelector(event.target.getAttribute('href'));
    if (target) {{
        target.scrollIntoView({{
            behavior: 'smooth'
        }});
    }}
}}

// Utility functions
function showMessage(message, type = 'info') {{
    console.log(`[${{type.toUpperCase()}}] ${{message}}`);
    // Could implement toast notifications here
}}

// Export for module use
if (typeof module !== 'undefined' && module.exports) {{
    module.exports = {{
        initializeInteractivity,
        showMessage
    }};
}}"""
    
    def _generate_python_content(self, action: WorkflowAction) -> str:
        """Generate Python content"""
        return f"""#!/usr/bin/env python3
\"\"\"
Generated Python file - {action.description}
\"\"\"

import os
import sys
from pathlib import Path

def main():
    \"\"\"Main function\"\"\"
    print("Generated Python application")
    print(f"Description: {action.description}")
    
    # Add main functionality here
    run_application()

def run_application():
    \"\"\"Run the main application logic\"\"\"
    print("Application running...")
    
    # Implement application logic here
    pass

def setup_environment():
    \"\"\"Setup application environment\"\"\"
    # Add environment setup code here
    pass

if __name__ == "__main__":
    setup_environment()
    main()"""
    
    async def _generate_file_modifications(self, action: WorkflowAction, current_content: str) -> str:
        """Generate file modifications using AI"""
        # This will be enhanced to use AI for intelligent modifications
        # For now, append modification comment
        
        modification_comment = f"\\n\\n<!-- Modified: {action.description} -->\\n"
        
        if action.file_path and action.file_path.endswith('.html'):
            # Insert before closing body tag
            if '</body>' in current_content:
                return current_content.replace('</body>', f'{modification_comment}</body>')
            else:
                return current_content + modification_comment
        else:
            return current_content + f"\\n\\n# Modified: {action.description}\\n"
