"""
Background job scheduler using APScheduler
"""
import asyncio
import uuid
from datetime import datetime, timezone
from typing import Optional, Dict, Any, Callable
from contextlib import asynccontextmanager

from apscheduler import AsyncScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.date import DateTrigger
from flask import current_app
import logging

from models import db, Job
from tools.llm_tools import LLMTools, LLMRequest
from tools.file_tools import FileTools

logger = logging.getLogger(__name__)

class JobScheduler:
    """Background job scheduler with APScheduler"""
    
    def __init__(self):
        self.scheduler: Optional[AsyncScheduler] = None
        self._running = False
    
    async def start(self):
        """Start the scheduler"""
        if self._running:
            logger.warning("Scheduler is already running")
            return
        
        try:
            # Create scheduler with configuration
            self.scheduler = AsyncScheduler()
            
            # Add some default jobs
            await self._add_default_jobs()
            
            # Start the scheduler
            await self.scheduler.start_in_background()
            self._running = True
            
            logger.info("Job scheduler started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start scheduler: {e}")
            raise
    
    async def stop(self):
        """Stop the scheduler"""
        if not self._running or not self.scheduler:
            return
        
        try:
            await self.scheduler.shutdown()
            self._running = False
            logger.info("Job scheduler stopped")
        except Exception as e:
            logger.error(f"Error stopping scheduler: {e}")
    
    async def _add_default_jobs(self):
        """Add default scheduled jobs"""
        try:
            # Example: Clean up old jobs every hour
            await self.scheduler.add_schedule(
                self._cleanup_old_jobs,
                IntervalTrigger(hours=1),
                id="cleanup_old_jobs"
            )
            
            # Example: Health check every 5 minutes
            await self.scheduler.add_schedule(
                self._health_check,
                IntervalTrigger(minutes=5),
                id="health_check"
            )
            
            logger.info("Default jobs added to scheduler")
            
        except Exception as e:
            logger.error(f"Error adding default jobs: {e}")
    
    async def _cleanup_old_jobs(self):
        """Clean up old completed jobs"""
        try:
            # This would need to be run in an app context
            # For now, just log the action
            logger.info("Running cleanup of old jobs")
            
            # In a real implementation, you'd:
            # 1. Get app context
            # 2. Query for old completed jobs
            # 3. Delete them from the database
            
        except Exception as e:
            logger.error(f"Error in cleanup job: {e}")
    
    async def _health_check(self):
        """Perform system health check"""
        try:
            logger.info("Running system health check")
            
            # In a real implementation, you'd:
            # 1. Check database connectivity
            # 2. Check external API availability
            # 3. Check disk space, memory, etc.
            # 4. Log any issues
            
        except Exception as e:
            logger.error(f"Error in health check: {e}")
    
    async def schedule_llm_job(
        self, 
        prompt: str, 
        model: str = "deepseek/deepseek-chat",
        delay_seconds: int = 0,
        job_id: Optional[str] = None
    ) -> str:
        """Schedule an LLM query job"""
        if not self.scheduler:
            raise RuntimeError("Scheduler not started")
        
        job_id = job_id or str(uuid.uuid4())
        
        try:
            # Create job record in database
            job_record = Job(
                job_id=job_id,
                job_type='llm_query',
                status='scheduled',
                input_data={
                    'prompt': prompt,
                    'model': model
                }
            )
            
            # Note: This would need proper app context in real implementation
            # db.session.add(job_record)
            # db.session.commit()
            
            # Schedule the job
            if delay_seconds > 0:
                trigger = DateTrigger(
                    run_date=datetime.now(timezone.utc).timestamp() + delay_seconds
                )
            else:
                trigger = DateTrigger(run_date=datetime.now(timezone.utc))
            
            await self.scheduler.add_schedule(
                self._execute_llm_job,
                trigger,
                args=[job_id, prompt, model],
                id=f"llm_job_{job_id}"
            )
            
            logger.info(f"Scheduled LLM job: {job_id}")
            return job_id
            
        except Exception as e:
            logger.error(f"Error scheduling LLM job: {e}")
            raise
    
    async def _execute_llm_job(self, job_id: str, prompt: str, model: str):
        """Execute an LLM query job"""
        try:
            logger.info(f"Executing LLM job: {job_id}")
            
            # Update job status to running
            # In real implementation, update database record
            
            # Create LLM request
            llm_request = LLMRequest(
                prompt=prompt,
                model=model,
                conversation_id=job_id
            )
            
            # Execute LLM query
            response = await LLMTools.query_llm(llm_request)
            
            # Update job with results
            # In real implementation, update database record with response
            
            logger.info(f"Completed LLM job: {job_id}")
            
        except Exception as e:
            logger.error(f"Error executing LLM job {job_id}: {e}")
            # In real implementation, update job status to failed
    
    async def schedule_file_processing_job(
        self, 
        file_path: str,
        operation: str,
        delay_seconds: int = 0,
        job_id: Optional[str] = None
    ) -> str:
        """Schedule a file processing job"""
        if not self.scheduler:
            raise RuntimeError("Scheduler not started")
        
        job_id = job_id or str(uuid.uuid4())
        
        try:
            # Create job record
            job_record = Job(
                job_id=job_id,
                job_type='file_processing',
                status='scheduled',
                input_data={
                    'file_path': file_path,
                    'operation': operation
                }
            )
            
            # Schedule the job
            if delay_seconds > 0:
                trigger = DateTrigger(
                    run_date=datetime.now(timezone.utc).timestamp() + delay_seconds
                )
            else:
                trigger = DateTrigger(run_date=datetime.now(timezone.utc))
            
            await self.scheduler.add_schedule(
                self._execute_file_processing_job,
                trigger,
                args=[job_id, file_path, operation],
                id=f"file_job_{job_id}"
            )
            
            logger.info(f"Scheduled file processing job: {job_id}")
            return job_id
            
        except Exception as e:
            logger.error(f"Error scheduling file processing job: {e}")
            raise
    
    async def _execute_file_processing_job(self, job_id: str, file_path: str, operation: str):
        """Execute a file processing job"""
        try:
            logger.info(f"Executing file processing job: {job_id}")
            
            # Update job status to running
            
            if operation == 'read':
                from tools.file_tools import FileReadRequest
                request = FileReadRequest(file_path=file_path)
                result = await FileTools.read_file(request)
            elif operation == 'analyze':
                # Example: analyze file content
                from tools.file_tools import FileReadRequest
                request = FileReadRequest(file_path=file_path)
                file_result = await FileTools.read_file(request)
                
                # Could analyze the content, count lines, etc.
                result = {
                    'file_size': file_result.file_size,
                    'line_count': len(file_result.content.splitlines()),
                    'word_count': len(file_result.content.split()),
                    'char_count': len(file_result.content)
                }
            else:
                raise ValueError(f"Unknown operation: {operation}")
            
            # Update job with results
            logger.info(f"Completed file processing job: {job_id}")
            
        except Exception as e:
            logger.error(f"Error executing file processing job {job_id}: {e}")
    
    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a scheduled job"""
        if not self.scheduler:
            return False
        
        try:
            # Try to remove from scheduler
            schedule_id = f"llm_job_{job_id}"
            try:
                await self.scheduler.remove_schedule(schedule_id)
                logger.info(f"Cancelled LLM job: {job_id}")
                return True
            except:
                pass
            
            # Try file processing job
            schedule_id = f"file_job_{job_id}"
            try:
                await self.scheduler.remove_schedule(schedule_id)
                logger.info(f"Cancelled file processing job: {job_id}")
                return True
            except:
                pass
            
            return False
            
        except Exception as e:
            logger.error(f"Error cancelling job {job_id}: {e}")
            return False
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get job status from database"""
        try:
            # In real implementation, query database for job
            # For now, return a placeholder
            return {
                'job_id': job_id,
                'status': 'unknown',
                'message': 'Job status lookup not implemented'
            }
        except Exception as e:
            logger.error(f"Error getting job status {job_id}: {e}")
            return None

# Global scheduler instance
job_scheduler = JobScheduler()

@asynccontextmanager
async def scheduler_lifespan():
    """Context manager for scheduler lifecycle"""
    try:
        await job_scheduler.start()
        yield job_scheduler
    finally:
        await job_scheduler.stop()
