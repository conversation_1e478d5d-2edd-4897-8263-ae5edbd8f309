"""
Project Management System
Handles project creation, switching, and file isolation
"""
import os
import json
import uuid
from pathlib import Path
from typing import List, Dict, Optional
from pydantic import BaseModel, Field
from datetime import datetime

class Project(BaseModel):
    """Represents a coding project"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str = ""
    created_at: datetime = Field(default_factory=datetime.now)
    last_accessed: datetime = Field(default_factory=datetime.now)
    file_count: int = 0
    folder_path: str = ""

class ProjectManager:
    """Manages coding projects and their isolation"""
    
    def __init__(self, workspace_root: str = "projects"):
        self.workspace_root = Path(workspace_root).resolve()
        self.workspace_root.mkdir(exist_ok=True)
        self.projects_file = self.workspace_root / "projects.json"
        self.current_project_file = self.workspace_root / "current_project.txt"
        
    def create_project(self, name: str, description: str = "") -> Project:
        """Create a new project with isolated folder"""
        # Sanitize project name for folder
        safe_name = "".join(c for c in name if c.isalnum() or c in (' ', '-', '_')).strip()
        safe_name = safe_name.replace(' ', '_')
        
        project = Project(
            name=name,
            description=description,
            folder_path=safe_name
        )
        
        # Create project folder
        project_folder = self.workspace_root / safe_name
        project_folder.mkdir(exist_ok=True)
        
        # Save project
        self._save_project(project)
        
        # Set as current project
        self.set_current_project(project.id)
        
        return project
    
    def get_projects(self) -> List[Project]:
        """Get all projects"""
        if not self.projects_file.exists():
            return []
        
        try:
            with open(self.projects_file, 'r') as f:
                projects_data = json.load(f)
            return [Project(**data) for data in projects_data]
        except Exception:
            return []
    
    def get_project(self, project_id: str) -> Optional[Project]:
        """Get specific project by ID"""
        projects = self.get_projects()
        for project in projects:
            if project.id == project_id:
                return project
        return None
    
    def get_current_project(self) -> Optional[Project]:
        """Get the currently active project"""
        if not self.current_project_file.exists():
            return None
        
        try:
            with open(self.current_project_file, 'r') as f:
                project_id = f.read().strip()
            return self.get_project(project_id)
        except Exception:
            return None
    
    def set_current_project(self, project_id: str) -> bool:
        """Set the current active project"""
        project = self.get_project(project_id)
        if not project:
            return False
        
        # Update last accessed time
        project.last_accessed = datetime.now()
        self._save_project(project)
        
        # Save current project ID
        with open(self.current_project_file, 'w') as f:
            f.write(project_id)
        
        return True
    
    def get_project_folder(self, project_id: Optional[str] = None) -> Path:
        """Get the folder path for a project (current project if None)"""
        if project_id is None:
            project = self.get_current_project()
        else:
            project = self.get_project(project_id)
        
        if not project:
            # Return default folder if no project
            default_folder = self.workspace_root / "default"
            default_folder.mkdir(exist_ok=True)
            return default_folder
        
        project_folder = self.workspace_root / project.folder_path
        project_folder.mkdir(exist_ok=True)
        return project_folder
    
    def update_project_file_count(self, project_id: Optional[str] = None, force: bool = False):
        """Update the file count for a project (Week 3 Performance Improvement)"""
        # Only update every 10th call or when forced (performance optimization)
        if not force:
            if not hasattr(self, '_update_counter'):
                self._update_counter = 0
            self._update_counter += 1

            # Only update every 10th call to avoid constant directory scanning
            if self._update_counter % 10 != 0:
                return

        if project_id is None:
            project = self.get_current_project()
        else:
            project = self.get_project(project_id)

        if not project:
            return

        project_folder = self.get_project_folder(project.id)
        file_count = len([f for f in project_folder.rglob('*') if f.is_file()])
        project.file_count = file_count
        self._save_project(project)
    
    def delete_project(self, project_id: str) -> bool:
        """Delete a project and its folder"""
        project = self.get_project(project_id)
        if not project:
            return False
        
        # Remove project folder
        project_folder = self.workspace_root / project.folder_path
        if project_folder.exists():
            import shutil
            shutil.rmtree(project_folder)
        
        # Remove from projects list
        projects = self.get_projects()
        projects = [p for p in projects if p.id != project_id]
        self._save_projects(projects)
        
        # If this was the current project, clear it
        current = self.get_current_project()
        if current and current.id == project_id:
            if self.current_project_file.exists():
                self.current_project_file.unlink()
        
        return True
    
    def _save_project(self, project: Project):
        """Save or update a single project"""
        projects = self.get_projects()
        
        # Update existing or add new
        updated = False
        for i, p in enumerate(projects):
            if p.id == project.id:
                projects[i] = project
                updated = True
                break
        
        if not updated:
            projects.append(project)
        
        self._save_projects(projects)
    
    def _save_projects(self, projects: List[Project]):
        """Save all projects to file"""
        projects_data = [p.model_dump() for p in projects]
        with open(self.projects_file, 'w') as f:
            json.dump(projects_data, f, indent=2, default=str)

# Global project manager instance
project_manager = ProjectManager()
