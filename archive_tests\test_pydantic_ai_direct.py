#!/usr/bin/env python3
"""
Test Pydantic AI directly
"""
import asyncio
import os
from dotenv import load_dotenv

async def test_pydantic_ai():
    """Test Pydantic AI directly"""
    try:
        # Load environment variables
        load_dotenv()
        
        from pydantic_ai.models.openai import OpenAIModel
        from pydantic_ai import Agent
        
        # Get API key
        openrouter_api_key = os.getenv('OPENROUTER_API_KEY')
        print(f"API Key found: {bool(openrouter_api_key)}")
        
        if not openrouter_api_key:
            print("❌ No API key found")
            return
        
        # Set environment variable
        os.environ['OPENROUTER_API_KEY'] = openrouter_api_key
        
        # Create model
        print("🔧 Creating OpenAI model with OpenRouter provider...")
        model = OpenAIModel(
            'deepseek/deepseek-r1-0528:free',
            provider='openrouter',
        )
        print("✅ Model created successfully")
        
        # Create agent
        print("🤖 Creating Pydantic AI agent...")
        agent = Agent(model)
        print("✅ Agent created successfully")
        
        # Test simple query
        print("💬 Testing simple query...")
        result = await agent.run("Say hello")
        print(f"✅ Response: {result.output}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_pydantic_ai())
