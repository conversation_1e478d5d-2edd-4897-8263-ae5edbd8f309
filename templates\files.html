{% extends "base.html" %}

{% block title %}Files - Flask AI App{% endblock %}

{% block content %}
<div class="p-6 h-full flex flex-col">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">File Manager</h1>
            <p class="text-gray-600">Manage your project files and let the AI help you code</p>
        </div>
        <div class="flex space-x-3">
            <button
                id="create-file-btn"
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2"
            >
                <span>📄</span>
                <span>New File</span>
            </button>
            <button
                id="refresh-files-btn"
                class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2"
            >
                <span>🔄</span>
                <span>Refresh</span>
            </button>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-6 min-h-0">
        <!-- File Tree Panel -->
        <div class="lg:col-span-1 bg-white rounded-lg shadow-sm border flex flex-col">
            <div class="border-b p-4">
                <h2 class="text-lg font-semibold text-gray-900">Project Files</h2>
            </div>
            
            <!-- File Tree -->
            <div class="flex-1 overflow-y-auto p-4">
                <div id="file-tree" class="space-y-1">
                    <div class="text-center text-gray-500 py-8">
                        <p>Loading files...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Content/Editor Panel -->
        <div class="lg:col-span-2 bg-white rounded-lg shadow-sm border flex flex-col">
            <!-- Editor Header -->
            <div class="border-b p-4 flex items-center justify-between">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">File Editor</h2>
                    <p class="text-sm text-gray-600" id="current-file-display">No file selected</p>
                </div>
                <div class="flex space-x-2">
                    <button 
                        id="save-current-file-btn"
                        class="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
                        disabled
                    >
                        Save
                    </button>
                    <button 
                        id="delete-current-file-btn"
                        class="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
                        disabled
                    >
                        Delete
                    </button>
                </div>
            </div>
            
            <!-- File Content Area -->
            <div class="flex-1 relative min-h-0">
                <!-- File Editor -->
                <textarea
                    id="file-content-editor"
                    class="w-full h-full p-4 font-mono text-sm border-none resize-none focus:outline-none"
                    placeholder="Start typing to create a new file, or select a file from the project tree to edit..."
                ></textarea>

                <!-- File Info Panel (for non-text files) -->
                <div id="file-info-panel" class="hidden w-full h-full p-4 flex flex-col items-center justify-center">
                    <div class="text-center">
                        <div class="text-6xl mb-4" id="file-icon">📄</div>
                        <h3 class="text-lg font-semibold text-gray-900" id="file-name">File Name</h3>
                        <p class="text-sm text-gray-600" id="file-details">File details</p>
                        <div class="mt-4 space-x-2">
                            <button 
                                id="download-file-btn"
                                class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                            >
                                Download
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Editor Status Bar -->
                <div class="absolute bottom-0 right-0 bg-gray-100 px-2 py-1 text-xs text-gray-600 border-l border-t">
                    <span id="file-editor-status">Ready</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create File Modal -->
<div id="create-file-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full">
            <div class="p-4 border-b">
                <h3 class="text-lg font-semibold">Create New File</h3>
            </div>
            <div class="p-4">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">File Path</label>
                    <input
                        type="text"
                        id="new-file-path"
                        placeholder="e.g., components/NewComponent.js"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Template</label>
                    <select 
                        id="file-template-select"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="empty">Empty File</option>
                        <option value="python">Python Script</option>
                        <option value="javascript">JavaScript File</option>
                        <option value="html">HTML Page</option>
                        <option value="css">CSS Stylesheet</option>
                        <option value="json">JSON File</option>
                        <option value="markdown">Markdown Document</option>
                        <option value="yaml">YAML Configuration</option>
                    </select>
                </div>
                <div class="flex justify-end space-x-2">
                    <button
                        id="cancel-create-file"
                        class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                        Cancel
                    </button>
                    <button
                        id="confirm-create-file"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                        Create File
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/app-utils.js') }}"></script>
<script src="{{ url_for('static', filename='js/files-manager.js') }}"></script>
{% endblock %}
