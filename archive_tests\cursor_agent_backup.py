"""
AI Coding Agent with Pydantic AI
Autonomous file creation, diff-based editing, async multi-file operations
"""
import asyncio
import json
import uuid
from typing import Optional, List, Dict, Any
from pathlib import Path
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
import logging
import difflib

logger = logging.getLogger(__name__)

class FileDiff(BaseModel):
    """Represents a file diff for preview"""
    file_path: str = Field(..., description="Path to the file")
    operation: str = Field(..., description="create, modify, delete")
    original_content: Optional[str] = Field(None, description="Original file content")
    new_content: str = Field(..., description="New file content")
    diff_lines: List[str] = Field(..., description="Unified diff lines")
    
class FileChange(BaseModel):
    """Represents a pending file change"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    file_path: str = Field(..., description="Path to the file")
    operation: str = Field(..., description="create, modify, delete")
    content: str = Field(..., description="New file content")
    description: str = Field(..., description="Description of the change")
    
class CodingSession(BaseModel):
    """Represents an active coding session with pending changes"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    pending_changes: List[FileChange] = Field(default_factory=list)
    applied_changes: List[FileChange] = Field(default_factory=list)
    
class WorkspaceState(BaseModel):
    """Current state of the workspace"""
    files: Dict[str, str] = Field(default_factory=dict)  # file_path -> content
    session: Optional[CodingSession] = Field(None)

# Global workspace state
workspace_state = WorkspaceState()

class AICodingAgent:
    """AI Coding Agent using Pydantic AI"""
    
    def __init__(self, workspace_root: str = None):
        # Use project manager to get current project folder
        if workspace_root is None:
            from tools.project_manager import project_manager
            self.workspace_root = project_manager.get_project_folder()
        else:
            self.workspace_root = Path(workspace_root).resolve()
        self.allowed_extensions = {
            '.py', '.js', '.html', '.css', '.json', '.xml', '.yaml', '.yml', 
            '.md', '.rst', '.txt', '.sql', '.sh', '.bat', '.ps1', '.ts', '.jsx', 
            '.tsx', '.vue', '.php', '.rb', '.go', '.rs', '.cpp', '.c', '.h', 
            '.java', '.kt', '.swift', '.dart', '.r', '.scala', '.clj', '.hs'
        }
        
        # Initialize Pydantic AI agent with DeepSeek through OpenRouter
        from pydantic_ai.models.openai import OpenAIModel
        import os
        from dotenv import load_dotenv

        # Load environment variables
        load_dotenv()

        # Use OpenRouter to access DeepSeek R1 free model
        openrouter_api_key = os.getenv('OPENROUTER_API_KEY')

        if not openrouter_api_key or openrouter_api_key == 'your-openrouter-api-key':
            logger.error("OpenRouter API key not found or invalid. Please set OPENROUTER_API_KEY in .env file")
            raise ValueError("OpenRouter API key not configured")

        # Use OpenAI model with OpenRouter provider
        model = OpenAIModel(
            'deepseek/deepseek-r1-0528:free',  # DeepSeek R1 free model through OpenRouter
            provider='openrouter',
        )

        # Set the API key via environment variable for OpenRouter
        os.environ['OPENROUTER_API_KEY'] = openrouter_api_key

        self.agent = Agent(
            model,
            deps_type=WorkspaceState,
            system_prompt=self._get_system_prompt(),
        )
        
        # Register tools
        self._register_tools()
    
    def _get_system_prompt(self) -> str:
        return """You are an AI coding assistant. You can autonomously create, modify, and manage files in a workspace.

When a user asks you to "make a website" or similar requests, you should:
1. Create multiple files as needed (HTML, CSS, JS, etc.)
2. Write complete, functional code
3. Organize files in a logical structure
4. Provide explanations for what you're building

Available tools:
- create_file: Create a new file with content
- read_file: Read existing file content  
- modify_file: Modify an existing file
- list_files: List files in directory
- delete_file: Delete a file

Always create complete, working code. Don't ask for permission - just build what the user requests."""

    def _register_tools(self):
        """Register all file operation tools"""
        
        @self.agent.tool
        async def create_file(ctx: RunContext[WorkspaceState], file_path: str, content: str, description: str = "") -> str:
            """Create a new file with the given content."""
            try:
                if not self._is_safe_path(file_path):
                    return f"Error: File path '{file_path}' is not allowed"
                
                full_path = self.workspace_root / file_path
                
                if full_path.exists():
                    return f"Error: File '{file_path}' already exists. Use modify_file to update it."
                
                # Create parent directories
                full_path.parent.mkdir(parents=True, exist_ok=True)
                
                # Write file
                full_path.write_text(content, encoding='utf-8')
                
                # Update workspace state
                ctx.deps.files[file_path] = content

                # Update project file count
                from tools.project_manager import project_manager
                project_manager.update_project_file_count()

                file_size = len(content.encode('utf-8'))
                return f"✅ Created file '{file_path}' ({file_size} bytes): {description}"
                
            except Exception as e:
                logger.error(f"Error creating file {file_path}: {e}")
                return f"Error creating file '{file_path}': {str(e)}"
        
        @self.agent.tool
        async def read_file(ctx: RunContext[WorkspaceState], file_path: str) -> str:
            """Read the content of an existing file."""
            try:
                if not self._is_safe_path(file_path):
                    return f"Error: File path '{file_path}' is not allowed"
                
                full_path = self.workspace_root / file_path
                
                if not full_path.exists():
                    return f"Error: File '{file_path}' does not exist"
                
                content = full_path.read_text(encoding='utf-8')
                
                # Update workspace state
                ctx.deps.files[file_path] = content
                
                return f"📄 Content of '{file_path}':\n\n{content}"
                
            except Exception as e:
                logger.error(f"Error reading file {file_path}: {e}")
                return f"Error reading file '{file_path}': {str(e)}"
        
        @self.agent.tool
        async def modify_file(ctx: RunContext[WorkspaceState], file_path: str, new_content: str, description: str = "") -> str:
            """Modify an existing file with new content."""
            try:
                if not self._is_safe_path(file_path):
                    return f"Error: File path '{file_path}' is not allowed"
                
                full_path = self.workspace_root / file_path
                
                if not full_path.exists():
                    return f"Error: File '{file_path}' does not exist. Use create_file to create it."
                
                # Read original content for diff
                original_content = full_path.read_text(encoding='utf-8')
                
                # Write new content
                full_path.write_text(new_content, encoding='utf-8')
                
                # Update workspace state
                ctx.deps.files[file_path] = new_content
                
                # Generate diff summary
                diff_lines = list(difflib.unified_diff(
                    original_content.splitlines(keepends=True),
                    new_content.splitlines(keepends=True),
                    fromfile=f"a/{file_path}",
                    tofile=f"b/{file_path}",
                    n=3
                ))
                
                diff_summary = f"Modified {len(diff_lines)} lines" if diff_lines else "No changes"
                file_size = len(new_content.encode('utf-8'))
                
                return f"✅ Modified file '{file_path}' ({file_size} bytes): {description}\n{diff_summary}"
                
            except Exception as e:
                logger.error(f"Error modifying file {file_path}: {e}")
                return f"Error modifying file '{file_path}': {str(e)}"
        
        @self.agent.tool
        async def list_files(ctx: RunContext[WorkspaceState], directory: str = ".") -> str:
            """List files in the specified directory."""
            try:
                if not self._is_safe_path(directory):
                    return f"Error: Directory path '{directory}' is not allowed"
                
                full_path = self.workspace_root / directory
                
                if not full_path.exists():
                    return f"Error: Directory '{directory}' does not exist"
                
                if not full_path.is_dir():
                    return f"Error: '{directory}' is not a directory"
                
                files = []
                for item in full_path.iterdir():
                    if item.is_file():
                        size = item.stat().st_size
                        files.append(f"📄 {item.name} ({size} bytes)")
                    elif item.is_dir():
                        files.append(f"📁 {item.name}/")
                
                if not files:
                    return f"Directory '{directory}' is empty"
                
                return f"📁 Files in '{directory}':\n" + "\n".join(sorted(files))
                
            except Exception as e:
                logger.error(f"Error listing directory {directory}: {e}")
                return f"Error listing directory '{directory}': {str(e)}"
        
        @self.agent.tool
        async def delete_file(ctx: RunContext[WorkspaceState], file_path: str, reason: str = "") -> str:
            """Delete a file."""
            try:
                if not self._is_safe_path(file_path):
                    return f"Error: File path '{file_path}' is not allowed"
                
                full_path = self.workspace_root / file_path
                
                if not full_path.exists():
                    return f"Error: File '{file_path}' does not exist"
                
                if full_path.is_dir():
                    return f"Error: '{file_path}' is a directory, not a file"
                
                file_size = full_path.stat().st_size
                full_path.unlink()
                
                # Remove from workspace state
                if file_path in ctx.deps.files:
                    del ctx.deps.files[file_path]
                
                return f"🗑️ Deleted file '{file_path}' ({file_size} bytes): {reason}"
                
            except Exception as e:
                logger.error(f"Error deleting file {file_path}: {e}")
                return f"Error deleting file '{file_path}': {str(e)}"
    
    def _is_safe_path(self, file_path: str) -> bool:
        """Check if file path is safe (within workspace and allowed extension)"""
        try:
            # Handle current directory
            if file_path == "." or file_path == "":
                return True
                
            full_path = (self.workspace_root / file_path).resolve()
            
            # Must be within workspace
            if not str(full_path).startswith(str(self.workspace_root)):
                return False
            
            # For directories, always allow
            if full_path.is_dir() or file_path.endswith('/') or file_path.endswith('\\'):
                return True
            
            # Must have allowed extension or be in allowed directories
            if full_path.suffix.lower() not in self.allowed_extensions:
                # Allow files in certain directories without extension checks
                allowed_dirs = {'templates', 'static', 'docs', 'tests', 'tools'}
                if not any(part in allowed_dirs for part in full_path.parts):
                    return False
            
            return True
        except Exception:
            return False
    
    async def process_request(self, prompt: str) -> str:
        """Process a coding request using the Pydantic AI agent"""
        try:
            # Add timeout to prevent hanging
            import asyncio
            logger.info(f"Processing request with Pydantic AI: {prompt[:100]}...")
            result = await asyncio.wait_for(
                self.agent.run(prompt, deps=workspace_state),
                timeout=15.0  # 15 second timeout
            )
            logger.info("Pydantic AI agent completed successfully")
            return result.output
        except asyncio.TimeoutError:
            logger.warning("Pydantic AI agent timed out, using fallback")
            return await self._fallback_process_request(prompt)
        except Exception as e:
            logger.error(f"Error processing request with Pydantic AI: {e}")
            logger.info("Using fallback method")
            # Fallback to manual tool calling approach
            return await self._fallback_process_request(prompt)

    async def _fallback_process_request(self, prompt: str) -> str:
        """Fallback processing using direct file operations without Flask context"""
        try:
            # For file creation requests, try to create the file directly
            if any(word in prompt.lower() for word in ['create', 'make', 'build', 'generate', 'write']):
                executed_operations = await self._execute_implied_operations("", prompt)
                if executed_operations:
                    return f"I've created the file for you!\n\n🔧 **Executed Operations:**\n{executed_operations}\n\nThe file has been created successfully in your current project. You can now view it in the file editor or ask me to modify it further."
                else:
                    # Try to extract more specific file info and create it
                    file_info = self._extract_file_info("", prompt)  # Empty AI response, use prompt for extraction
                    if file_info:
                        file_path, content = file_info
                        try:
                            full_path = self.workspace_root / file_path
                            full_path.parent.mkdir(parents=True, exist_ok=True)
                            full_path.write_text(content, encoding='utf-8')

                            # Update project file count
                            from tools.project_manager import project_manager
                            project_manager.update_project_file_count()

                            file_size = len(content.encode('utf-8'))
                            return f"✅ Created file '{file_path}' ({file_size} bytes)\n\nI've created the file you requested in your current project. The file contains:\n\n```\n{content[:200]}{'...' if len(content) > 200 else ''}\n```\n\nYou can now view and edit it in the file editor!"
                        except Exception as e:
                            return f"I tried to create the file but encountered an error: {str(e)}\n\nPlease make sure the filename is valid and try again."
                    else:
                        return "I'd be happy to create a file for you! Please specify:\n- The filename (e.g., 'index.html', 'script.js', 'app.py')\n- What type of content you'd like in the file\n\nFor example: 'Create an HTML file called index.html with a welcome message'"

            # For reading/listing requests
            if any(word in prompt.lower() for word in ['list', 'show', 'read', 'view', 'see']):
                try:
                    files = []
                    for item in self.workspace_root.iterdir():
                        if not item.name.startswith('.'):
                            files.append(f"{'📁' if item.is_dir() else '📄'} {item.name}")

                    if files:
                        return f"📁 **Files in your current project:**\n\n" + "\n".join(files) + f"\n\nFound {len(files)} items. You can ask me to create, modify, or read any of these files!"
                    else:
                        return "📁 **Your project is empty**\n\nNo files found in your current project. You can ask me to create files like:\n- 'Create an HTML file'\n- 'Make a Python script'\n- 'Build a CSS stylesheet'"
                except Exception:
                    return "I can help you manage files in your project. Ask me to create, read, or modify files!"

            # For other requests, provide a helpful response
            return f"I'm your AI coding assistant! I can help you with:\n\n🔧 **File Operations:**\n- Create files: 'Create an HTML file called index.html'\n- Read files: 'Show me the content of app.py'\n- List files: 'What files are in my project?'\n\n💡 **What would you like me to help you build today?**"

        except Exception as e:
            logger.error(f"Error in fallback processing: {e}")
            return f"I'm ready to help you code! However, I encountered an issue: {str(e)}\n\nPlease try asking me to create or modify specific files."

    async def _execute_implied_operations(self, ai_response: str, original_prompt: str) -> str:
        """Execute file operations implied by the AI response"""
        operations = []

        # Simple pattern matching for common operations
        if "create" in original_prompt.lower() and ("file" in original_prompt.lower() or "html" in original_prompt.lower() or "css" in original_prompt.lower() or "js" in original_prompt.lower()):
            # Try to extract file name and content from response
            file_info = self._extract_file_info(ai_response, original_prompt)
            if file_info:
                file_path, content = file_info
                try:
                    full_path = self.workspace_root / file_path
                    full_path.parent.mkdir(parents=True, exist_ok=True)
                    full_path.write_text(content, encoding='utf-8')
                    operations.append(f"✅ Created file '{file_path}' ({len(content)} bytes)")
                except Exception as e:
                    operations.append(f"❌ Failed to create '{file_path}': {str(e)}")

        return "\n".join(operations)

    def _extract_file_info(self, ai_response: str, original_prompt: str) -> Optional[tuple[str, str]]:
        """Extract file name and content from AI response"""
        import re

        # Try to extract filename from prompt
        filename_patterns = [
            r"create.*?(?:file|called)\s+['\"]?([^'\"\s]+\.[a-zA-Z]+)['\"]?",
            r"(?:file|called)\s+['\"]?([^'\"\s]+\.[a-zA-Z]+)['\"]?",
            r"['\"]?([^'\"\s]+\.(?:html|css|js|py|json|md|txt))['\"]?"
        ]

        filename = None
        for pattern in filename_patterns:
            match = re.search(pattern, original_prompt, re.IGNORECASE)
            if match:
                filename = match.group(1)
                break

        if not filename:
            return None

        # Try to extract code content from response
        code_patterns = [
            r"```(?:html|css|javascript|js|python|py)?\s*\n(.*?)\n```",
            r"<html.*?</html>",
            r"<!DOCTYPE.*?</html>",
        ]

        content = None
        for pattern in code_patterns:
            match = re.search(pattern, ai_response, re.DOTALL | re.IGNORECASE)
            if match:
                content = match.group(1) if match.groups() else match.group(0)
                break

        # If no code blocks found, try to generate basic content based on file type
        if not content:
            ext = filename.split('.')[-1].lower()
            if ext == 'html':
                content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hello World</title>
</head>
<body>
    <h1>Hello World!</h1>
    <p>This file was created by AI Coder.</p>
</body>
</html>"""
            elif ext == 'css':
                content = """/* CSS file created by AI Coder */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

h1 {
    color: #333;
    text-align: center;
}"""
            elif ext == 'js':
                content = """// JavaScript file created by AI Coder
console.log('Hello from AI Coder!');

function greet(name) {
    return `Hello, ${name}!`;
}

// Example usage
document.addEventListener('DOMContentLoaded', function() {
    console.log(greet('World'));
});"""
            elif ext == 'py':
                content = """#!/usr/bin/env python3
# Python file created by AI Coder

def main():
    print("Hello from AI Coder!")

if __name__ == "__main__":
    main()"""

        return (filename, content) if content else None

    def run_sync(self, prompt: str) -> str:
        """Synchronous wrapper for process_request"""
        return asyncio.run(self.process_request(prompt))
