/**
 * File editor functionality for the Flask AI App
 */

class FileEditor {
    constructor() {
        this.currentFile = null;
        this.saveTimeout = null;
        this.initializeElements();
        this.bindEvents();
    }

    initializeElements() {
        // File editor elements
        this.openFileButton = document.getElementById('open-file-button');
        this.saveFileButton = document.getElementById('save-file-button');
        this.newFileButton = document.getElementById('new-file-button');
        this.codeEditor = document.getElementById('code-editor');
        this.currentFilePath = document.getElementById('current-file-path');
        this.editorStatus = document.getElementById('editor-status');

        // File modal elements
        this.fileModal = document.getElementById('file-modal');
        this.filePathInput = document.getElementById('file-path-input');
        this.loadFileButton = document.getElementById('load-file-button');
        this.createFileButton = document.getElementById('create-file-button');
        this.cancelFileButton = document.getElementById('cancel-file-button');
        this.modalTitle = document.getElementById('modal-title');

        this.isCreatingNewFile = false;
    }

    bindEvents() {
        // File editor buttons
        this.openFileButton.addEventListener('click', () => this.showFileModal(false));
        this.saveFileButton.addEventListener('click', () => this.saveFile());
        this.newFileButton.addEventListener('click', () => this.showFileModal(true));

        // File modal buttons
        this.loadFileButton.addEventListener('click', () => this.loadFile());
        this.createFileButton.addEventListener('click', () => this.createFile());
        this.cancelFileButton.addEventListener('click', () => this.hideFileModal());

        // File path input
        this.filePathInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                if (this.isCreatingNewFile) {
                    this.createFile();
                } else {
                    this.loadFile();
                }
            }
        });

        // Code editor changes
        this.codeEditor.addEventListener('input', () => this.handleEditorChange());

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));

        // Close modal when clicking outside
        this.fileModal.addEventListener('click', (e) => {
            if (e.target === this.fileModal) {
                this.hideFileModal();
            }
        });
    }

    async showFileModal(isCreating = false) {
        this.isCreatingNewFile = isCreating;

        if (isCreating) {
            this.modalTitle.textContent = 'Create New File';
            this.loadFileButton.classList.add('hidden');
            this.createFileButton.classList.remove('hidden');
            this.filePathInput.placeholder = 'Enter new file path (e.g., components/NewComponent.js)';
        } else {
            this.modalTitle.textContent = 'Open File';
            this.loadFileButton.classList.remove('hidden');
            this.createFileButton.classList.add('hidden');
            this.filePathInput.placeholder = 'Enter file path (e.g., app.py)';

            // Load and display project files
            await this.loadProjectFiles();
        }

        this.fileModal.classList.remove('hidden');
        this.filePathInput.focus();
    }

    hideFileModal() {
        this.fileModal.classList.add('hidden');
        this.filePathInput.value = '';
        // Clear file browser if it exists
        const fileBrowser = this.fileModal.querySelector('.file-browser');
        if (fileBrowser) {
            fileBrowser.remove();
        }
    }

    async loadProjectFiles() {
        try {
            // Get current project files using the list-workspace endpoint
            const response = await AppUtils.apiRequest('/api/ai-coder/list-workspace?directory=.');

            if (response.success && response.content) {
                const files = JSON.parse(response.content);
                // Filter to only show files (not directories) for the file browser
                const fileList = files.filter(item => item.is_file);
                this.displayFileBrowser(fileList);
            }
        } catch (error) {
            console.error('Failed to load project files:', error);
        }
    }

    displayFileBrowser(files) {
        // Remove existing file browser
        const existingBrowser = this.fileModal.querySelector('.file-browser');
        if (existingBrowser) {
            existingBrowser.remove();
        }

        // Create file browser
        const fileBrowser = document.createElement('div');
        fileBrowser.className = 'file-browser mt-4 max-h-64 overflow-y-auto border border-gray-300 rounded-lg';

        const browserTitle = document.createElement('div');
        browserTitle.className = 'bg-gray-100 px-3 py-2 border-b border-gray-300 font-medium text-sm';
        browserTitle.textContent = 'Project Files (click to select):';
        fileBrowser.appendChild(browserTitle);

        const fileList = document.createElement('div');
        fileList.className = 'divide-y divide-gray-200';

        if (files.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'px-3 py-4 text-gray-500 text-center text-sm';
            emptyMessage.textContent = 'No files in current project';
            fileList.appendChild(emptyMessage);
        } else {
            files.forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.className = 'px-3 py-2 hover:bg-blue-50 cursor-pointer flex items-center space-x-2 text-sm';

                const fileIcon = document.createElement('span');
                fileIcon.textContent = this.getFileIcon(file.name);

                const fileName = document.createElement('span');
                fileName.textContent = file.name;
                fileName.className = 'flex-1';

                const fileSize = document.createElement('span');
                fileSize.textContent = AppUtils.formatFileSize(file.size);
                fileSize.className = 'text-gray-500 text-xs';

                fileItem.appendChild(fileIcon);
                fileItem.appendChild(fileName);
                fileItem.appendChild(fileSize);

                fileItem.addEventListener('click', () => {
                    this.filePathInput.value = file.name;
                    // Highlight selected file
                    fileList.querySelectorAll('.file-item').forEach(item => {
                        item.classList.remove('bg-blue-100');
                    });
                    fileItem.classList.add('bg-blue-100');
                });

                fileItem.classList.add('file-item');
                fileList.appendChild(fileItem);
            });
        }

        fileBrowser.appendChild(fileList);

        // Insert file browser after the input field
        const inputContainer = this.filePathInput.parentElement;
        inputContainer.appendChild(fileBrowser);
    }

    getFileIcon(fileName) {
        const extension = '.' + fileName.split('.').pop().toLowerCase();
        const icons = {
            '.py': '🐍',
            '.js': '📜',
            '.html': '🌐',
            '.css': '🎨',
            '.json': '📋',
            '.md': '📝',
            '.txt': '📄',
            '.yaml': '⚙️',
            '.yml': '⚙️',
            '.sql': '🗃️',
            '.png': '🖼️',
            '.jpg': '🖼️',
            '.jpeg': '🖼️',
            '.gif': '🖼️',
            '.svg': '🖼️',
            '.pdf': '📕'
        };
        return icons[extension] || '📄';
    }

    async loadFile() {
        const filePath = this.filePathInput.value.trim();
        if (!filePath) return;

        try {
            this.editorStatus.textContent = 'Loading...';

            // Use project-aware API endpoint
            const response = await AppUtils.apiRequest('/api/ai-coder/file-operation', {
                method: 'POST',
                body: JSON.stringify({
                    operation: 'read',
                    file_path: filePath
                })
            });

            if (response.success) {
                this.codeEditor.value = response.content;
                this.currentFilePath.textContent = response.file_path;
                this.currentFile = response.file_path;
                this.saveFileButton.disabled = false;
                this.editorStatus.textContent = `Loaded (${AppUtils.formatFileSize(response.file_size || response.content.length)})`;

                this.hideFileModal();

                AppUtils.showNotification(`File loaded: ${response.file_path}`, 'success');
            } else {
                throw new Error(response.error || response.message || 'Failed to load file');
            }

        } catch (error) {
            this.editorStatus.textContent = 'Error loading file';
            AppUtils.showNotification(`Failed to load file: ${error.message}`, 'error');
        }
    }

    async createFile() {
        const filePath = this.filePathInput.value.trim();
        if (!filePath) return;

        try {
            this.editorStatus.textContent = 'Creating...';

            // Use AI Coder API to create file
            const response = await AppUtils.apiRequest('/api/ai-coder/file-operation', {
                method: 'POST',
                body: JSON.stringify({
                    operation: 'create',
                    file_path: filePath,
                    content: this.getDefaultContent(filePath)
                })
            });

            if (response.success) {
                // Load the created file into editor
                this.codeEditor.value = response.content || this.getDefaultContent(filePath);
                this.currentFilePath.textContent = response.file_path;
                this.currentFile = response.file_path;
                this.saveFileButton.disabled = false;
                this.editorStatus.textContent = `Created (${response.file_size || 0} bytes)`;

                this.hideFileModal();
                AppUtils.showNotification(`File created: ${response.file_path}`, 'success');
            } else {
                this.editorStatus.textContent = 'Error creating file';
                AppUtils.showNotification(`Failed to create file: ${response.error || response.message}`, 'error');
            }

        } catch (error) {
            this.editorStatus.textContent = 'Error creating file';
            AppUtils.showNotification(`Failed to create file: ${error.message}`, 'error');
        }
    }

    getDefaultContent(filePath) {
        const extension = AppUtils.getFileExtension(filePath);

        const templates = {
            'py': '#!/usr/bin/env python3\n"""\nNew Python file\n"""\n\ndef main():\n    pass\n\nif __name__ == "__main__":\n    main()\n',
            'js': '/**\n * New JavaScript file\n */\n\n',
            'html': '<!DOCTYPE html>\n<html lang="en">\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <title>New Page</title>\n</head>\n<body>\n    <h1>Hello World</h1>\n</body>\n</html>\n',
            'css': '/* New CSS file */\n\n',
            'json': '{\n    "name": "new-file",\n    "version": "1.0.0"\n}\n',
            'md': '# New Markdown File\n\nContent goes here...\n',
            'txt': 'New text file\n',
            'sql': '-- New SQL file\n\n',
            'yaml': '# New YAML file\nname: new-file\nversion: 1.0.0\n',
            'yml': '# New YAML file\nname: new-file\nversion: 1.0.0\n'
        };

        return templates[extension] || `// New ${extension.toUpperCase()} file\n\n`;
    }

    async saveFile() {
        if (!this.currentFile) return;

        try {
            this.editorStatus.textContent = 'Saving...';

            // Use project-aware API endpoint
            const response = await AppUtils.apiRequest('/api/ai-coder/file-operation', {
                method: 'POST',
                body: JSON.stringify({
                    operation: 'write',
                    file_path: this.currentFile,
                    content: this.codeEditor.value
                })
            });

            if (response.success) {
                this.editorStatus.textContent = `Saved (${AppUtils.formatFileSize(response.file_size || this.codeEditor.value.length)})`;
                AppUtils.showNotification('File saved successfully!', 'success');
            } else {
                throw new Error(response.error || response.message || 'Failed to save file');
            }

        } catch (error) {
            this.editorStatus.textContent = 'Error saving file';
            AppUtils.showNotification(`Failed to save file: ${error.message}`, 'error');
        }
    }

    handleEditorChange() {
        if (this.currentFile) {
            this.editorStatus.textContent = 'Modified';
            
            // Clear existing timeout
            clearTimeout(this.saveTimeout);
            
            // Set new timeout for auto-save indicator
            this.saveTimeout = setTimeout(() => {
                this.editorStatus.textContent = 'Ready';
            }, 2000);
        }
    }

    handleKeyboardShortcuts(e) {
        // Ctrl+S for saving
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            if (!this.saveFileButton.disabled) {
                this.saveFile();
            }
        }

        // Ctrl+O for opening file
        if (e.ctrlKey && e.key === 'o') {
            e.preventDefault();
            this.showFileModal();
        }

        // Escape to close modal
        if (e.key === 'Escape') {
            if (!this.fileModal.classList.contains('hidden')) {
                this.hideFileModal();
            }
        }
    }

    /**
     * Get current file content
     * @returns {string} - Current editor content
     */
    getCurrentContent() {
        return this.codeEditor.value;
    }

    /**
     * Set editor content
     * @param {string} content - Content to set
     */
    setContent(content) {
        this.codeEditor.value = content;
        this.handleEditorChange();
    }

    /**
     * Clear the editor
     */
    clear() {
        this.codeEditor.value = '';
        this.currentFile = null;
        this.currentFilePath.textContent = 'No file selected';
        this.saveFileButton.disabled = true;
        this.editorStatus.textContent = 'Ready';
    }

    /**
     * Check if editor has unsaved changes
     * @returns {boolean} - Whether there are unsaved changes
     */
    hasUnsavedChanges() {
        return this.editorStatus.textContent === 'Modified';
    }

    /**
     * Get current file path
     * @returns {string|null} - Current file path or null
     */
    getCurrentFilePath() {
        return this.currentFile;
    }

    /**
     * Insert text at cursor position
     * @param {string} text - Text to insert
     */
    insertTextAtCursor(text) {
        const start = this.codeEditor.selectionStart;
        const end = this.codeEditor.selectionEnd;
        const currentValue = this.codeEditor.value;
        
        this.codeEditor.value = currentValue.substring(0, start) + text + currentValue.substring(end);
        
        // Move cursor to end of inserted text
        const newCursorPos = start + text.length;
        this.codeEditor.setSelectionRange(newCursorPos, newCursorPos);
        
        this.handleEditorChange();
        this.codeEditor.focus();
    }

    /**
     * Get selected text
     * @returns {string} - Selected text
     */
    getSelectedText() {
        const start = this.codeEditor.selectionStart;
        const end = this.codeEditor.selectionEnd;
        return this.codeEditor.value.substring(start, end);
    }

    /**
     * Replace selected text
     * @param {string} replacement - Text to replace selection with
     */
    replaceSelectedText(replacement) {
        const start = this.codeEditor.selectionStart;
        const end = this.codeEditor.selectionEnd;
        const currentValue = this.codeEditor.value;
        
        this.codeEditor.value = currentValue.substring(0, start) + replacement + currentValue.substring(end);
        
        // Select the replacement text
        this.codeEditor.setSelectionRange(start, start + replacement.length);
        
        this.handleEditorChange();
        this.codeEditor.focus();
    }
}

// Initialize file editor when DOM is loaded
let fileEditor;
document.addEventListener('DOMContentLoaded', () => {
    fileEditor = new FileEditor();
});
