#!/usr/bin/env python3
"""
Test AI coding integration with Flask app
"""
import asyncio
import httpx

async def test_ai_coding_integration():
    """Test the complete AI coding experience"""
    base_url = "http://localhost:5000"
    
    print("🚀 Testing AI Coding Integration")
    print("=" * 60)
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            
            # Test 1: Simple coding request
            print("💬 Test 1: Simple Coding Request")
            print("-" * 40)
            
            response1 = await client.post(
                f"{base_url}/api/ai-coder/process",
                headers={"Content-Type": "application/json"},
                json={
                    "prompt": "Create a simple HTML page called welcome.html with a welcome message",
                    "context": None
                }
            )
            
            if response1.status_code == 200:
                result1 = response1.json()
                print(f"✅ AI Coder Response: {result1['response'][:300]}...")
            else:
                print(f"❌ Error: {response1.status_code}")
                print(f"Response: {response1.text}")
                return
            
            await asyncio.sleep(2)
            
            # Test 2: Multi-file website creation
            print("\n🌐 Test 2: Multi-file Website Creation")
            print("-" * 40)
            
            response2 = await client.post(
                f"{base_url}/api/ai-coder/process",
                headers={"Content-Type": "application/json"},
                json={
                    "prompt": "Make a complete website with HTML, CSS, and JavaScript. Create index.html, styles.css, and script.js files for a personal portfolio website",
                    "context": "Create a modern, responsive portfolio website"
                }
            )
            
            if response2.status_code == 200:
                result2 = response2.json()
                print(f"✅ Multi-file Creation: {result2['response'][:300]}...")
            else:
                print(f"❌ Error: {response2.status_code}")
                return
            
            await asyncio.sleep(2)
            
            # Test 3: Chat integration with coding detection
            print("\n💬 Test 3: Chat Integration")
            print("-" * 40)
            
            response3 = await client.post(
                f"{base_url}/api/llm/query",
                headers={"Content-Type": "application/json"},
                json={
                    "prompt": "Create a Python script that calculates fibonacci numbers",
                    "model": "deepseek/deepseek-r1-0528:free"
                }
            )
            
            if response3.status_code == 200:
                result3 = response3.json()
                print(f"✅ Chat Integration: {result3['response'][:300]}...")
            else:
                print(f"❌ Error: {response3.status_code}")
                return
            
            # Test 4: File listing to see created files
            print("\n📁 Test 4: File Listing")
            print("-" * 40)
            
            response4 = await client.get(f"{base_url}/api/ai-coder/list-workspace")
            
            if response4.status_code == 200:
                result4 = response4.json()
                print(f"✅ File Listing: {result4['success']}")
                if result4['content']:
                    import json
                    files = json.loads(result4['content'])
                    print(f"📂 Found {len(files)} items:")
                    for file in files[:10]:  # Show first 10 files
                        icon = "📁" if file['is_dir'] else "📄"
                        size = f" ({file['size']} bytes)" if file['is_file'] and file['size'] else ""
                        print(f"  {icon} {file['name']}{size}")
            else:
                print(f"❌ Error: {response4.status_code}")
                return
            
            print(f"\n🎉 All AI coding tests passed!")
            print("💡 Key features working:")
            print("  ✅ AI coding agent")
            print("  ✅ Autonomous file creation")
            print("  ✅ Multi-file project generation")
            print("  ✅ Chat integration with coding detection")
            print("  ✅ File management and listing")
            print("  ✅ Fallback handling for API issues")

            print(f"\n🌟 Your AI coding environment is ready!")
            print("🔗 Try it at: http://localhost:5000")
            print("💬 Chat: Ask 'Make a website' or 'Create a Python script'")
            print("📁 Files: View and manage created files at /files")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")

async def main():
    """Main test function"""
    print("🚀 Testing AI Coding Integration")
    print("Make sure the Flask app is running at http://localhost:5000\n")

    await test_ai_coding_integration()
    
    print("\n✨ Test complete!")

if __name__ == "__main__":
    asyncio.run(main())
