/**
 * AI Activity Animations - Real-time visual feedback for AI operations
 * Provides intelligent, contextual animations based on AI activity types
 */

class ActivityAnimationManager {
    constructor() {
        this.currentActivity = null;
        this.animationContainer = null;
        this.isActive = false;
        this.animationFrameId = null;
        
        // Animation configurations for each activity type
        this.animations = {
            thinking: {
                icon: '🧠',
                color: '#6366f1',
                text: 'Thinking',
                animation: 'pulse',
                particles: true
            },
            analyzing: {
                icon: '🔍',
                color: '#8b5cf6',
                text: 'Analyzing',
                animation: 'scan',
                particles: false
            },
            coding: {
                icon: '💻',
                color: '#10b981',
                text: 'Coding',
                animation: 'type',
                particles: true
            },
            writing: {
                icon: '✍️',
                color: '#f59e0b',
                text: 'Writing',
                animation: 'write',
                particles: false
            },
            reading: {
                icon: '📖',
                color: '#3b82f6',
                text: 'Reading',
                animation: 'flip',
                particles: false
            },
            modifying: {
                icon: '🔧',
                color: '#ef4444',
                text: 'Modifying',
                animation: 'wrench',
                particles: true
            },
            creating: {
                icon: '✨',
                color: '#ec4899',
                text: 'Creating',
                animation: 'sparkle',
                particles: true
            },
            searching: {
                icon: '🔎',
                color: '#06b6d4',
                text: 'Searching',
                animation: 'search',
                particles: false
            },
            calculating: {
                icon: '🧮',
                color: '#84cc16',
                text: 'Calculating',
                animation: 'calculate',
                particles: true
            },
            debugging: {
                icon: '🐛',
                color: '#f97316',
                text: 'Debugging',
                animation: 'debug',
                particles: false
            },
            planning: {
                icon: '📋',
                color: '#6366f1',
                text: 'Planning',
                animation: 'plan',
                particles: false
            },
            optimizing: {
                icon: '⚡',
                color: '#eab308',
                text: 'Optimizing',
                animation: 'lightning',
                particles: true
            },
            testing: {
                icon: '🧪',
                color: '#8b5cf6',
                text: 'Testing',
                animation: 'test',
                particles: false
            },
            compiling: {
                icon: '⚙️',
                color: '#64748b',
                text: 'Compiling',
                animation: 'gear',
                particles: true
            },
            deploying: {
                icon: '🚀',
                color: '#10b981',
                text: 'Deploying',
                animation: 'rocket',
                particles: true
            },
            learning: {
                icon: '🎓',
                color: '#3b82f6',
                text: 'Learning',
                animation: 'learn',
                particles: false
            },
            processing: {
                icon: '⚡',
                color: '#6b7280',
                text: 'Processing',
                animation: 'process',
                particles: true
            },
            generating: {
                icon: '🎨',
                color: '#ec4899',
                text: 'Generating',
                animation: 'generate',
                particles: true
            },
            validating: {
                icon: '✅',
                color: '#10b981',
                text: 'Validating',
                animation: 'check',
                particles: false
            },
            finalizing: {
                icon: '🏁',
                color: '#059669',
                text: 'Finalizing',
                animation: 'finish',
                particles: true
            }
        };
        
        this.initializeContainer();
        this.setupEventListeners();
    }
    
    initializeContainer() {
        // Create the activity animation container
        this.animationContainer = document.createElement('div');
        this.animationContainer.id = 'ai-activity-container';
        this.animationContainer.className = 'ai-activity-container hidden';
        this.animationContainer.innerHTML = `
            <div class="ai-activity-content">
                <div class="ai-activity-icon"></div>
                <div class="ai-activity-text">
                    <div class="ai-activity-title"></div>
                    <div class="ai-activity-description"></div>
                    <div class="ai-activity-progress">
                        <div class="ai-activity-progress-bar"></div>
                    </div>
                </div>
                <div class="ai-activity-particles"></div>
            </div>
        `;
        
        // Insert into chat container
        const chatContainer = document.querySelector('.chat-container') || document.body;
        chatContainer.appendChild(this.animationContainer);
        
        console.log('🎬 Activity animation container initialized');
    }
    
    setupEventListeners() {
        console.log('🎬 Setting up activity animation event listeners');

        // Note: Event listeners are now set up in base.html to ensure proper timing
        // This method is kept for future use or manual event handling

        // Start polling for activity updates as fallback
        this.startPolling();

        // Test the animation system
        setTimeout(() => {
            console.log('🧪 Testing activity animation system...');
            this.testAnimation();
        }, 2000);
    }

    startPolling() {
        console.log('🔄 Toast activity polling DISABLED - using chat activity system instead');
        // DISABLED: Toast system polling is now handled by the chat activity manager
        // This prevents duplicate API calls and conflicting activity displays
        return;
    }

    testAnimation() {
        console.log('🧪 Running activity animation test');

        // Test with a sample activity
        const testActivity = {
            activity_type: 'thinking',
            description: 'Testing the animation system',
            progress: 0.5,
            metadata: { test: true }
        };

        console.log('🧪 Starting test animation:', testActivity);
        this.handleActivityEvent(testActivity);

        // Complete the test after 3 seconds
        setTimeout(() => {
            testActivity.progress = 1.0;
            testActivity.description = 'Test completed successfully!';
            console.log('🧪 Completing test animation');
            this.handleActivityEvent(testActivity);
        }, 3000);
    }
    
    handleActivityEvent(activityData) {
        const { activity_type, description, progress, metadata } = activityData;
        
        console.log(`🎯 Activity event: ${activity_type} - ${description}`);
        
        if (progress === null || progress === undefined) {
            // Start new activity
            this.startActivity(activity_type, description, metadata);
        } else if (progress >= 1.0) {
            // Complete activity
            this.completeActivity(description);
        } else {
            // Update progress
            this.updateProgress(progress, description);
        }
    }
    
    startActivity(activityType, description, metadata = {}) {
        const config = this.animations[activityType] || this.animations.processing;
        
        this.currentActivity = {
            type: activityType,
            config: config,
            description: description,
            metadata: metadata,
            startTime: Date.now()
        };
        
        this.isActive = true;
        
        // Update UI elements
        const container = this.animationContainer;
        const icon = container.querySelector('.ai-activity-icon');
        const title = container.querySelector('.ai-activity-title');
        const desc = container.querySelector('.ai-activity-description');
        const progressBar = container.querySelector('.ai-activity-progress-bar');
        
        icon.textContent = config.icon;
        icon.style.color = config.color;
        title.textContent = config.text;
        desc.textContent = description;
        progressBar.style.width = '0%';
        progressBar.style.backgroundColor = config.color;
        
        // Apply animation class
        container.className = `ai-activity-container active ${config.animation}`;
        container.style.setProperty('--activity-color', config.color);
        
        // Start particles if enabled
        if (config.particles) {
            this.startParticles();
        }
        
        console.log(`🚀 Started ${activityType} activity: ${description}`);
    }
    
    updateProgress(progress, description = null) {
        if (!this.isActive || !this.currentActivity) return;
        
        const progressBar = this.animationContainer.querySelector('.ai-activity-progress-bar');
        const desc = this.animationContainer.querySelector('.ai-activity-description');
        
        // Smooth progress animation
        progressBar.style.width = `${Math.round(progress * 100)}%`;
        
        if (description) {
            desc.textContent = description;
            this.currentActivity.description = description;
        }
        
        console.log(`📊 Progress: ${Math.round(progress * 100)}% - ${description || this.currentActivity.description}`);
    }
    
    completeActivity(finalDescription = null) {
        if (!this.isActive || !this.currentActivity) return;
        
        const desc = this.animationContainer.querySelector('.ai-activity-description');
        const progressBar = this.animationContainer.querySelector('.ai-activity-progress-bar');
        
        // Final updates
        progressBar.style.width = '100%';
        if (finalDescription) {
            desc.textContent = finalDescription;
        }
        
        console.log(`✅ Completed ${this.currentActivity.type}: ${finalDescription || this.currentActivity.description}`);
        
        // Fade out after a brief delay
        setTimeout(() => {
            this.hideActivity();
        }, 1500);
    }
    
    hideActivity() {
        if (this.animationContainer) {
            this.animationContainer.className = 'ai-activity-container hidden';
            this.stopParticles();
        }
        
        this.isActive = false;
        this.currentActivity = null;
        
        console.log('🎬 Activity animation hidden');
    }
    
    startParticles() {
        const particlesContainer = this.animationContainer.querySelector('.ai-activity-particles');
        if (!particlesContainer || !this.currentActivity) return;
        
        const config = this.currentActivity.config;
        
        // Create floating particles
        const createParticle = () => {
            const particle = document.createElement('div');
            particle.className = 'ai-particle';
            particle.style.backgroundColor = config.color;
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDuration = (2 + Math.random() * 3) + 's';
            particle.style.animationDelay = Math.random() * 2 + 's';
            
            particlesContainer.appendChild(particle);
            
            // Remove particle after animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 5000);
        };
        
        // Create particles periodically
        this.particleInterval = setInterval(createParticle, 500);
        
        // Create initial burst
        for (let i = 0; i < 3; i++) {
            setTimeout(createParticle, i * 200);
        }
    }
    
    stopParticles() {
        if (this.particleInterval) {
            clearInterval(this.particleInterval);
            this.particleInterval = null;
        }
        
        // Clear existing particles
        const particlesContainer = this.animationContainer?.querySelector('.ai-activity-particles');
        if (particlesContainer) {
            particlesContainer.innerHTML = '';
        }
    }
    
    // Public API methods
    forceHide() {
        this.hideActivity();
    }

    isCurrentlyActive() {
        return this.isActive;
    }

    getCurrentActivity() {
        return this.currentActivity;
    }

    cleanup() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
        this.stopParticles();
        this.hideActivity();
    }
}

// DISABLED: Toast activity animation manager - using chat activity system instead
// window.activityAnimationManager = new ActivityAnimationManager();

console.log('🎬 AI Activity Animation Manager DISABLED - using chat activity system');
