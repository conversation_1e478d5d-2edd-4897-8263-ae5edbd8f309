#!/usr/bin/env python3
"""
Test the new project management system
"""
import asyncio
import httpx

async def test_project_system():
    """Test the complete project management system"""
    base_url = "http://localhost:5000"
    
    print("🚀 Testing Project Management System")
    print("=" * 60)
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            
            # Test 1: Create a new project
            print("📁 Test 1: Create New Project")
            print("-" * 40)
            
            response1 = await client.post(
                f"{base_url}/api/projects",
                headers={"Content-Type": "application/json"},
                json={
                    "name": "My Website Project",
                    "description": "A cool website with HTML, CSS, and JavaScript"
                }
            )
            
            if response1.status_code == 200:
                result1 = response1.json()
                project_id = result1['project']['id']
                print(f"✅ Project Created: {result1['project']['name']}")
                print(f"   ID: {project_id}")
                print(f"   Folder: {result1['project']['folder_path']}")
            else:
                print(f"❌ Error: {response1.status_code}")
                print(f"Response: {response1.text}")
                return
            
            await asyncio.sleep(1)
            
            # Test 2: Get current project
            print("\n📋 Test 2: Get Current Project")
            print("-" * 40)
            
            response2 = await client.get(f"{base_url}/api/projects/current")
            
            if response2.status_code == 200:
                result2 = response2.json()
                if result2['current_project']:
                    print(f"✅ Current Project: {result2['current_project']['name']}")
                else:
                    print("ℹ️ No current project set")
            else:
                print(f"❌ Error: {response2.status_code}")
                return
            
            await asyncio.sleep(1)
            
            # Test 3: Create files in the project using AI Coder
            print("\n🤖 Test 3: AI Coder File Creation in Project")
            print("-" * 40)
            
            response3 = await client.post(
                f"{base_url}/api/ai-coder/process",
                headers={"Content-Type": "application/json"},
                json={
                    "prompt": "Create a simple HTML file called index.html with a welcome message",
                    "context": "This is for my website project"
                }
            )
            
            if response3.status_code == 200:
                result3 = response3.json()
                print(f"✅ AI Coder Response: {result3['response'][:200]}...")
            else:
                print(f"❌ Error: {response3.status_code}")
                return
            
            await asyncio.sleep(2)
            
            # Test 4: List project files
            print("\n📂 Test 4: List Project Files")
            print("-" * 40)
            
            response4 = await client.get(f"{base_url}/api/ai-coder/list-workspace")
            
            if response4.status_code == 200:
                result4 = response4.json()
                print(f"✅ File Listing Success: {result4['success']}")
                if result4['content']:
                    import json
                    files = json.loads(result4['content'])
                    print(f"📂 Found {len(files)} items in project:")
                    for file in files:
                        icon = "📁" if file['is_dir'] else "📄"
                        size = f" ({file['size']} bytes)" if file['is_file'] and file['size'] else ""
                        print(f"  {icon} {file['name']}{size}")
            else:
                print(f"❌ Error: {response4.status_code}")
                return
            
            await asyncio.sleep(1)
            
            # Test 5: Create another project
            print("\n📁 Test 5: Create Second Project")
            print("-" * 40)
            
            response5 = await client.post(
                f"{base_url}/api/projects",
                headers={"Content-Type": "application/json"},
                json={
                    "name": "Python Scripts",
                    "description": "Collection of useful Python scripts"
                }
            )
            
            if response5.status_code == 200:
                result5 = response5.json()
                project2_id = result5['project']['id']
                print(f"✅ Second Project Created: {result5['project']['name']}")
                print(f"   ID: {project2_id}")
            else:
                print(f"❌ Error: {response5.status_code}")
                return
            
            await asyncio.sleep(1)
            
            # Test 6: List all projects
            print("\n📋 Test 6: List All Projects")
            print("-" * 40)
            
            response6 = await client.get(f"{base_url}/api/projects")
            
            if response6.status_code == 200:
                result6 = response6.json()
                projects = result6['projects']
                current_id = result6['current_project_id']
                print(f"✅ Found {len(projects)} projects:")
                for project in projects:
                    status = " (CURRENT)" if project['id'] == current_id else ""
                    print(f"  📁 {project['name']}{status}")
                    print(f"     Files: {project['file_count']} | Created: {project['created_at'][:10]}")
            else:
                print(f"❌ Error: {response6.status_code}")
                return
            
            await asyncio.sleep(1)
            
            # Test 7: Switch back to first project
            print("\n🔄 Test 7: Switch Projects")
            print("-" * 40)
            
            response7 = await client.post(f"{base_url}/api/projects/{project_id}/switch")
            
            if response7.status_code == 200:
                result7 = response7.json()
                print(f"✅ Switched to: {result7['project']['name']}")
            else:
                print(f"❌ Error: {response7.status_code}")
                return
            
            print(f"\n🎉 All project management tests passed!")
            print("💡 Key features working:")
            print("  ✅ Project creation with isolated folders")
            print("  ✅ Current project tracking")
            print("  ✅ AI Coder integration with projects")
            print("  ✅ Project-scoped file operations")
            print("  ✅ Project listing and management")
            print("  ✅ Project switching")
            
            print(f"\n🌟 Your Project Management System is ready!")
            print("🔗 Try it at: http://localhost:5000")
            print("📁 Click 'New Project' to create projects")
            print("🔄 Click 'Projects' to switch between them")
            print("🤖 AI will create files only in the current project!")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")

async def main():
    """Main test function"""
    print("🚀 Testing Project Management System")
    print("Make sure the Flask app is running at http://localhost:5000\n")
    
    await test_project_system()
    
    print("\n✨ Test complete!")

if __name__ == "__main__":
    asyncio.run(main())
