#!/usr/bin/env python3
"""
Flask AI App startup script
"""
import os
import sys
from pathlib import Path

def check_environment():
    """Check if environment is properly set up"""
    print("Checking environment...")
    
    # Check if .env file exists
    if not Path('.env').exists():
        print("⚠️  .env file not found. Creating from template...")
        if Path('.env.example').exists():
            import shutil
            shutil.copy('.env.example', '.env')
            print("✓ Created .env file from template")
            print("📝 Please edit .env file and add your OPENROUTER_API_KEY")
        else:
            print("❌ .env.example not found. Please create .env file manually.")
            return False
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Virtual environment not detected. Consider using a virtual environment.")
    
    return True

def setup_database():
    """Initialize database"""
    print("Setting up database...")
    
    try:
        from app import create_app
        from models import db
        
        app = create_app()
        with app.app_context():
            db.create_all()
            print("✓ Database initialized")
        
        return True
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False

def main():
    """Main startup function"""
    print("Flask AI App Startup")
    print("=" * 40)
    
    # Check environment
    if not check_environment():
        return False
    
    # Setup database
    if not setup_database():
        return False
    
    # Import and run app
    try:
        from app import create_app
        
        app = create_app()
        
        print("\n🚀 Starting Flask AI App...")
        print("📍 URL: http://localhost:5000")
        print("🛑 Press Ctrl+C to stop")
        print("-" * 40)
        
        # Run the app
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 Flask AI App stopped")
    except Exception as e:
        print(f"❌ Failed to start app: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
