#!/usr/bin/env python3
"""
Debug the workflow execution issue
"""

import sys
import os
import asyncio
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.unified_agent import get_unified_agent

async def debug_workflow():
    """Debug the workflow execution"""
    print("DEBUG: Starting workflow debug")
    
    try:
        agent = get_unified_agent()
        print("DEBUG: Agent initialized")
        
        # Test workflow analysis
        prompt = "create a very creative website"
        print(f"DEBUG: Testing prompt: {prompt}")
        
        # Test the workflow analyzer directly
        workflow_request = await agent.workflow_analyzer.analyze_request_with_toggle(prompt)
        
        print(f"DEBUG: Workflow analysis result:")
        print(f"  requires_workflow: {workflow_request.requires_workflow}")
        print(f"  complexity_score: {workflow_request.complexity_score}")
        print(f"  workflow_plan: {workflow_request.workflow_plan}")
        
        if workflow_request.workflow_plan:
            print(f"  plan title: {workflow_request.workflow_plan.title}")
            print(f"  plan steps: {len(workflow_request.workflow_plan.steps)}")
            
            # Check each step
            for i, step in enumerate(workflow_request.workflow_plan.steps, 1):
                print(f"    Step {i}: {type(step)}")
                if hasattr(step, 'description'):
                    print(f"      Description: {step.description}")
                if hasattr(step, 'action_type'):
                    print(f"      Action: {step.action_type}")
                if hasattr(step, 'file_path'):
                    print(f"      File: {step.file_path}")
        
        return True
        
    except Exception as e:
        print(f"DEBUG: Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(debug_workflow())
