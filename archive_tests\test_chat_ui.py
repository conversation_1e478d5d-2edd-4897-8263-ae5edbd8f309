#!/usr/bin/env python3
"""
Test the chat UI with multiple messages to verify scrolling behavior
"""
import asyncio
import httpx
import time

async def test_chat_scrolling():
    """Test chat interface with multiple messages to verify scrolling"""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing Chat UI Scrolling Behavior")
    print("=" * 50)
    
    # Test messages to create a long conversation
    test_messages = [
        "Hello! Can you help me test the chat interface?",
        "Please write a short paragraph about Python programming.",
        "What are the benefits of using Flask for web development?",
        "Can you explain what async/await does in Python?",
        "Write a simple function that calculates the factorial of a number.",
        "What's the difference between a list and a tuple in Python?",
        "How do you handle errors in Python using try/except?",
        "Explain what a decorator is in Python with an example.",
        "What are the main features of object-oriented programming?",
        "How do you work with files in Python?"
    ]
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print("📝 Sending multiple messages to test scrolling...")
            
            for i, message in enumerate(test_messages, 1):
                print(f"\n🔄 Sending message {i}/{len(test_messages)}: {message[:50]}...")
                
                response = await client.post(
                    f"{base_url}/api/llm/query",
                    headers={"Content-Type": "application/json"},
                    json={
                        "prompt": message,
                        "model": "deepseek/deepseek-r1-0528:free"
                    }
                )
                
                if response.status_code == 200:
                    result = response.json()
                    response_text = result.get('response', '')
                    tokens = result.get('tokens_used', 0)
                    
                    print(f"✅ Response received ({tokens} tokens)")
                    print(f"📝 Preview: {response_text[:100]}...")
                    
                    # Small delay between messages
                    await asyncio.sleep(1)
                else:
                    print(f"❌ Error: {response.status_code}")
                    print(f"Response: {response.text}")
                    break
            
            print(f"\n🎉 Test completed! Sent {len(test_messages)} messages.")
            print("💡 Check the browser - the chat should be scrollable with all messages visible!")
            print("🔍 The chat container should maintain its height and scroll smoothly.")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")

async def main():
    """Main test function"""
    print("🚀 Starting Chat UI Scroll Test")
    print("Make sure the Flask app is running at http://localhost:5000")
    print("Open your browser to see the chat interface while this test runs.\n")
    
    # Wait a moment for user to prepare
    await asyncio.sleep(2)
    
    await test_chat_scrolling()
    
    print("\n✨ Test complete! Check your browser to see the scrollable chat interface.")

if __name__ == "__main__":
    asyncio.run(main())
