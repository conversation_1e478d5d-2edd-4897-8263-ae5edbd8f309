#!/usr/bin/env python3
"""
Test detailed file modification
"""
import asyncio
import httpx

async def test_detailed_modification():
    """Test detailed file modification"""
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            print("🔧 Testing Detailed File Modification...")
            
            # Test: Modify with specific instructions
            print("\n1. Testing specific modification...")
            response = await client.post(
                'http://localhost:5000/api/unified-ai/process',
                headers={'Content-Type': 'application/json'},
                json={
                    "prompt": "Modify the index.html file to change the title from 'Chocolate Café' to 'Amazing Chocolate Shop' and also change the main heading",
                    "conversation_id": None,
                    "context": {}
                }
            )
            
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', 'No response')
                print(f"Response: {response_text}")
                
                # Check for modification tool indicators
                if any(indicator in response_text for indicator in ['✅', 'Successfully modified', 'bytes', 'Modification:']):
                    print("✅ TOOL CALLED: File modification tool was used!")
                else:
                    print("❌ TOOL NOT CALLED: Generic response, tool may not have been used")
                    
                print(f"Success: {data.get('success', False)}")
            else:
                print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_detailed_modification())
