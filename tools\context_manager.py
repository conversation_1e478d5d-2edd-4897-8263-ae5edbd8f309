"""
Chat Context Management System
Implements intelligent context tracking with Pydantic AI message history
Week 4 Major Enhancement: VITALLY IMPORTANT context management
Week 5 REVOLUTIONARY: Intelligent Auto-Compaction System
"""
import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
from pydantic_core import to_json
from pydantic_ai.messages import ModelMessage, ModelMessagesTypeAdapter
from pydantic_ai.usage import Usage

logger = logging.getLogger(__name__)

class ChatContextManager:
    """
    Manages chat context with intelligent token tracking and history management
    Implements best practices from Pydantic AI documentation
    Week 5 REVOLUTIONARY: Intelligent Auto-Compaction System
    """

    def __init__(self, max_context_tokens: int = 150000):  # Leave buffer for 160k limit
        self.max_context_tokens = max_context_tokens
        self.conversation_contexts: Dict[str, List[ModelMessage]] = {}
        self.conversation_tokens: Dict[str, int] = {}

        # Week 5 REVOLUTIONARY: Auto-compaction settings
        self.auto_compact_threshold = 0.90  # Compact at 90% capacity
        self.retention_window = 6  # Keep last 6 messages intact (production setting)
        self.progressive_compaction = True  # Enable progressive compaction for long conversations
        self.last_activity: Dict[str, datetime] = {}  # Track activity for intelligent timing
        self.active_sessions: Dict[str, bool] = {}  # Track if session is actively coding
        self.compaction_in_progress: Dict[str, bool] = {}  # Prevent concurrent compaction
        
    def get_conversation_id(self, project_id: Optional[str] = None) -> str:
        """Generate or retrieve conversation ID - FIXED for unique chat sessions"""
        # Generate unique conversation ID for each chat session
        import uuid
        base_id = project_id if project_id else "default"
        unique_id = str(uuid.uuid4())[:8]  # Short unique identifier
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        return f"conv_{base_id}_{timestamp}_{unique_id}"
    
    def add_message_to_context(
        self,
        conversation_id: str,
        messages: List[ModelMessage],
        usage: Optional[Usage] = None
    ) -> None:
        """Add new messages to conversation context - STRICT isolation"""
        try:
            if not conversation_id:
                logger.error("❌ No conversation_id provided to add_message_to_context - CONTEXT BLEEDING RISK!")
                return

            if conversation_id not in self.conversation_contexts:
                self.conversation_contexts[conversation_id] = []
                self.conversation_tokens[conversation_id] = 0
                self.last_activity[conversation_id] = datetime.now()
                self.active_sessions[conversation_id] = False
                self.compaction_in_progress[conversation_id] = False
                logger.info(f"🆕 Created new context for conversation: {conversation_id}")

            # Add new messages
            self.conversation_contexts[conversation_id].extend(messages)
            logger.info(f"➕ Added {len(messages)} messages to conversation: {conversation_id}")

            # Update activity tracking
            self.last_activity[conversation_id] = datetime.now()
            self._detect_active_session(conversation_id, messages)

            # Update token count
            if usage:
                self.conversation_tokens[conversation_id] += usage.total_tokens
                logger.info(f"📊 Context updated: {usage.total_tokens} tokens added, total: {self.conversation_tokens[conversation_id]} for {conversation_id}")

            # Check for intelligent auto-compaction (non-blocking)
            try:
                self._check_auto_compaction(conversation_id)
            except Exception as e:
                logger.error(f"❌ Error in auto-compaction check: {e}")

        except Exception as e:
            logger.error(f"❌ Error adding message to context: {e}")
    
    def get_message_history(self, conversation_id: str) -> List[ModelMessage]:
        """Get message history for conversation - STRICT isolation with database fallback"""
        if not conversation_id:
            logger.warning("⚠️ No conversation_id provided to get_message_history")
            return []

        # Check if context exists in memory
        if conversation_id not in self.conversation_contexts:
            # Try to restore from database
            logger.info(f"🔄 Context not in memory, attempting to restore from database: {conversation_id}")
            self._restore_context_from_database(conversation_id)

        messages = self.conversation_contexts.get(conversation_id, [])
        logger.info(f"📋 Retrieved {len(messages)} messages for conversation: {conversation_id}")
        return messages
    
    def _restore_context_from_database(self, conversation_id: str) -> None:
        """Restore context from database when not in memory"""
        try:
            from models import LLMConversation
            from app import db

            # Get the most recent conversation entry for this conversation_id
            latest_conversation = db.session.query(LLMConversation)\
                .filter_by(conversation_id=conversation_id)\
                .order_by(LLMConversation.created_at.desc())\
                .first()

            if latest_conversation and latest_conversation.message_history:
                # Deserialize the message history
                messages = self.deserialize_messages(latest_conversation.message_history)

                if messages:
                    # Restore context
                    self.conversation_contexts[conversation_id] = messages
                    self.conversation_tokens[conversation_id] = latest_conversation.cumulative_tokens or 0
                    self.last_activity[conversation_id] = latest_conversation.created_at
                    self.active_sessions[conversation_id] = False
                    self.compaction_in_progress[conversation_id] = False

                    logger.info(f"✅ Restored context from database: {len(messages)} messages, {self.conversation_tokens[conversation_id]} tokens")
                else:
                    logger.warning(f"⚠️ Failed to deserialize messages for conversation: {conversation_id}")
            else:
                logger.info(f"📝 No existing context found in database for conversation: {conversation_id}")

        except Exception as e:
            logger.error(f"❌ Error restoring context from database: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

    def get_context_stats(self, conversation_id: str) -> Dict[str, Any]:
        """Get context statistics for display with database fallback"""
        # Ensure context is loaded
        if conversation_id not in self.conversation_contexts:
            self._restore_context_from_database(conversation_id)

        messages = self.conversation_contexts.get(conversation_id, [])
        tokens = self.conversation_tokens.get(conversation_id, 0)
        
        return {
            'conversation_id': conversation_id,
            'message_count': len(messages),
            'total_tokens': tokens,
            'max_tokens': self.max_context_tokens,
            'token_percentage': (tokens / self.max_context_tokens) * 100 if self.max_context_tokens > 0 else 0,
            'context_health': self._get_context_health(tokens)
        }
    
    def _get_context_health(self, tokens: int) -> str:
        """Get context health status"""
        percentage = (tokens / self.max_context_tokens) * 100
        
        if percentage < 50:
            return "excellent"
        elif percentage < 75:
            return "good"
        elif percentage < 90:
            return "warning"
        else:
            return "critical"
    
    def _detect_active_session(self, conversation_id: str, messages: List[ModelMessage]) -> None:
        """Detect if user is in an active coding session"""
        try:
            # Analyze recent messages for coding activity patterns
            coding_indicators = [
                'create', 'edit', 'modify', 'fix', 'debug', 'implement', 'add',
                'function', 'class', 'method', 'variable', 'import', 'def',
                'async', 'await', 'return', 'if', 'for', 'while', 'try',
                'error', 'exception', 'bug', 'issue', 'problem'
            ]

            recent_content = ""
            for msg in messages[-3:]:  # Check last 3 messages
                if hasattr(msg, 'content') and msg.content:
                    recent_content += str(msg.content).lower() + " "

            # Count coding indicators
            coding_score = sum(1 for indicator in coding_indicators if indicator in recent_content)

            # Mark as active if high coding activity
            self.active_sessions[conversation_id] = coding_score >= 3

            if self.active_sessions[conversation_id]:
                logger.info(f"🔥 Active coding session detected for {conversation_id} (score: {coding_score})")

        except Exception as e:
            logger.error(f"❌ Error detecting active session: {e}")
            self.active_sessions[conversation_id] = False

    def _check_auto_compaction(self, conversation_id: str) -> None:
        """Enhanced auto-compaction with progressive thresholds"""
        try:
            current_tokens = self.conversation_tokens.get(conversation_id, 0)
            token_percentage = (current_tokens / self.max_context_tokens) * 100

            # Progressive auto-compaction thresholds
            if token_percentage >= 95:
                # Critical threshold - compact immediately regardless of activity
                logger.info(f"🚨 Critical auto-compaction triggered at {token_percentage:.1f}%")
                self._perform_intelligent_compaction(conversation_id)
            elif token_percentage >= (self.auto_compact_threshold * 100):
                # Standard threshold - check if safe
                logger.info(f"🎯 Auto-compaction threshold reached: {token_percentage:.1f}%")
                if self._is_safe_to_compact(conversation_id):
                    logger.info(f"✅ Safe to compact - triggering intelligent compaction")
                    self._perform_intelligent_compaction(conversation_id)
                else:
                    logger.info(f"⏳ Delaying compaction - active session detected")
            elif token_percentage >= 80:
                # Early warning - prepare for compaction
                logger.info(f"⚠️ Context approaching capacity: {token_percentage:.1f}% - preparing for compaction")

        except Exception as e:
            logger.error(f"❌ Error checking auto-compaction: {e}")

    def _is_safe_to_compact(self, conversation_id: str) -> bool:
        """Determine if it's safe to compact context"""
        try:
            # Don't compact if already in progress
            if self.compaction_in_progress.get(conversation_id, False):
                return False

            # Don't compact during active coding sessions
            if self.active_sessions.get(conversation_id, False):
                return False

            # Don't compact if recent activity (within last 2 minutes)
            last_activity = self.last_activity.get(conversation_id)
            if last_activity and (datetime.now() - last_activity).seconds < 120:
                return False

            return True

        except Exception as e:
            logger.error(f"❌ Error checking compaction safety: {e}")
            return False

    def _trim_context_if_needed(self, conversation_id: str) -> None:
        """Legacy trim method - replaced by intelligent compaction"""
        current_tokens = self.conversation_tokens.get(conversation_id, 0)

        # Only use legacy trim as emergency fallback
        if current_tokens > self.max_context_tokens * 1.1:  # 110% - emergency only
            logger.warning(f"🚨 Emergency context trim triggered for {conversation_id}")
            self._perform_emergency_trim(conversation_id)
    
    def _perform_intelligent_compaction(self, conversation_id: str) -> None:
        """Perform intelligent context compaction"""
        try:
            self.compaction_in_progress[conversation_id] = True
            logger.info(f"🧠 Starting intelligent compaction for {conversation_id}")

            messages = self.conversation_contexts.get(conversation_id, [])
            logger.info(f"🔍 Compaction analysis: {len(messages)} total messages, retention window: {self.retention_window}")

            # Progressive compaction: adjust retention based on conversation length
            if self.progressive_compaction and len(messages) > 20:
                # For very long conversations, keep more recent context
                dynamic_retention = min(8, self.retention_window + 2)
                logger.info(f"📈 Progressive compaction: using retention window of {dynamic_retention}")
            else:
                dynamic_retention = self.retention_window

            if len(messages) <= dynamic_retention:
                logger.info(f"📝 Too few messages to compact ({len(messages)} <= {dynamic_retention})")
                return

            # Keep system messages and recent messages
            system_messages = [msg for msg in messages[:2] if hasattr(msg, 'role') and getattr(msg, 'role', '') == 'system']
            recent_messages = messages[-dynamic_retention:]

            # Messages to compact (middle section)
            messages_to_compact = messages[len(system_messages):-dynamic_retention]

            if not messages_to_compact:
                logger.info(f"📝 No messages to compact")
                return

            # Enhanced compaction: create intelligent summary
            logger.info(f"📝 Compacting {len(messages_to_compact)} middle messages")

            # Create intelligent summary of compacted messages
            summary = self._create_intelligent_summary(messages_to_compact)

            # Create a simple summary message as a dict (compatible with existing structure)
            summary_message = {
                'role': 'system',
                'content': f"[COMPACTED CONTEXT SUMMARY - {len(messages_to_compact)} messages]\n{summary}",
                'timestamp': datetime.now().isoformat()
            }

            # Create new context with summary
            new_context = system_messages + [summary_message] + recent_messages

            # Recalculate tokens for the new context
            old_tokens = self.conversation_tokens.get(conversation_id, 0)
            new_tokens = self._estimate_tokens(new_context)

            # Update the context
            self.conversation_contexts[conversation_id] = new_context
            self.conversation_tokens[conversation_id] = new_tokens

            reduction_percent = ((old_tokens - new_tokens) / old_tokens * 100) if old_tokens > 0 else 0

            logger.info(f"🧠 Compaction completed: {len(messages)} → {len(new_context)} messages")
            logger.info(f"📊 Token reduction: {old_tokens} → {new_tokens} ({reduction_percent:.1f}% reduction)")
            logger.info(f"✨ Intelligent compaction complete with {reduction_percent:.1f}% token reduction")

        except Exception as e:
            logger.error(f"❌ Error during intelligent compaction: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
        finally:
            self.compaction_in_progress[conversation_id] = False

    def _estimate_tokens(self, messages: List[ModelMessage]) -> int:
        """Estimate token count for a list of messages"""
        try:
            total_chars = 0
            for msg in messages:
                if hasattr(msg, 'content'):
                    content = getattr(msg, 'content', '')
                elif isinstance(msg, dict):
                    content = msg.get('content', '')
                else:
                    content = str(msg)
                total_chars += len(str(content))

            # Rough estimation: ~4 characters per token
            return total_chars // 4
        except Exception as e:
            logger.error(f"❌ Error estimating tokens: {e}")
            return 0

    def _create_intelligent_summary(self, messages: List[ModelMessage]) -> str:
        """Create an enhanced intelligent summary of messages"""
        try:
            # Extract key information from messages
            topics = set()
            actions = set()
            files_mentioned = set()
            user_requests = []
            key_decisions = []

            for msg in messages:
                # Handle different message formats
                content = ""
                role = "unknown"

                if hasattr(msg, 'content'):
                    content = str(getattr(msg, 'content', ''))
                    role = str(getattr(msg, 'role', 'unknown'))
                elif isinstance(msg, dict):
                    content = str(msg.get('content', ''))
                    role = str(msg.get('role', 'unknown'))
                else:
                    content = str(msg)

                # Skip very short messages
                if len(content) < 15:
                    continue

                # Extract user requests (first 80 chars)
                if role == 'user':
                    clean_request = content.strip()[:80]
                    if clean_request and len(clean_request) > 10:
                        user_requests.append(clean_request)

                # Extract actions and activities
                content_lower = content.lower()
                if any(word in content_lower for word in ['create', 'implement', 'build', 'develop']):
                    actions.add("created/implemented features")
                if any(word in content_lower for word in ['fix', 'debug', 'error', 'issue', 'problem']):
                    actions.add("debugging and fixes")
                if any(word in content_lower for word in ['optimize', 'improve', 'enhance', 'refactor']):
                    actions.add("optimization and improvements")
                if any(word in content_lower for word in ['test', 'testing', 'verify']):
                    actions.add("testing and verification")
                if any(word in content_lower for word in ['security', 'auth', 'permission']):
                    actions.add("security enhancements")
                if any(word in content_lower for word in ['ui', 'interface', 'design', 'layout']):
                    actions.add("UI/UX improvements")

                # Extract technical topics
                if any(word in content_lower for word in ['website', 'web', 'html', 'css']):
                    topics.add("web development")
                if any(word in content_lower for word in ['database', 'sql', 'data']):
                    topics.add("database work")
                if any(word in content_lower for word in ['api', 'endpoint', 'request']):
                    topics.add("API development")
                if any(word in content_lower for word in ['context', 'compaction', 'token']):
                    topics.add("context management")
                if any(word in content_lower for word in ['ai', 'llm', 'model']):
                    topics.add("AI integration")

                # Extract file references (improved pattern)
                import re
                file_patterns = re.findall(r'\b[\w\-\.]+\.(py|js|html|css|json|txt|md|yml|yaml|xml|sql|ts|jsx|tsx)\b', content, re.IGNORECASE)
                files_mentioned.update(file_patterns)

                # Extract key decisions or important statements
                if any(phrase in content_lower for phrase in ['decided to', 'implemented', 'changed to', 'added', 'removed']):
                    decision = content.strip()[:100]
                    if len(decision) > 20:
                        key_decisions.append(decision)

            # Build intelligent summary
            summary_parts = []

            # Add main user request
            if user_requests:
                main_request = user_requests[0]
                if len(user_requests) > 1:
                    summary_parts.append(f"User: {main_request}... (+{len(user_requests)-1} more requests)")
                else:
                    summary_parts.append(f"User: {main_request}")

            # Add AI actions
            if actions:
                action_list = sorted(list(actions))[:3]  # Top 3 actions
                summary_parts.append(f"AI: {', '.join(action_list)}")

            # Add technical topics
            if topics:
                topic_list = sorted(list(topics))[:3]  # Top 3 topics
                summary_parts.append(f"Focus: {', '.join(topic_list)}")

            # Add files involved
            if files_mentioned:
                file_list = sorted(list(files_mentioned))[:4]  # Top 4 files
                summary_parts.append(f"Files: {', '.join(file_list)}")

            # Create final summary
            if summary_parts:
                summary = " | ".join(summary_parts)
            else:
                summary = "General development conversation and assistance"

            return f"{summary} (Compacted {len(messages)} messages)"

        except Exception as e:
            logger.error(f"❌ Error creating intelligent summary: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return f"Previous conversation (Compacted {len(messages)} messages)"

    def _perform_emergency_trim(self, conversation_id: str) -> None:
        """Emergency trim when context exceeds limits"""
        try:
            messages = self.conversation_contexts[conversation_id]

            if len(messages) > 4:
                # Keep first 2 (system prompts) and last 4 (recent conversation)
                trimmed_messages = messages[:2] + messages[-4:]
                self.conversation_contexts[conversation_id] = trimmed_messages

                # Recalculate tokens (rough estimate)
                current_tokens = self.conversation_tokens[conversation_id]
                self.conversation_tokens[conversation_id] = int(current_tokens * 0.6)

                logger.warning(f"🚨 Emergency trim: {len(messages)} -> {len(trimmed_messages)} messages")

        except Exception as e:
            logger.error(f"❌ Error during emergency trim: {e}")

    def compact_context(self, conversation_id: str) -> Dict[str, Any]:
        """Manual context compaction (for UI button) with database fallback"""
        try:
            # Ensure context is loaded
            if conversation_id not in self.conversation_contexts:
                self._restore_context_from_database(conversation_id)

            if conversation_id not in self.conversation_contexts:
                return {'success': False, 'error': 'Conversation not found'}

            if self.compaction_in_progress.get(conversation_id, False):
                return {'success': False, 'error': 'Compaction already in progress'}

            old_stats = self.get_context_stats(conversation_id)
            self._perform_intelligent_compaction(conversation_id)
            new_stats = self.get_context_stats(conversation_id)

            return {
                'success': True,
                'old_tokens': old_stats['total_tokens'],
                'new_tokens': new_stats['total_tokens'],
                'reduction_percentage': ((old_stats['total_tokens'] - new_stats['total_tokens']) / old_stats['total_tokens'] * 100) if old_stats['total_tokens'] > 0 else 0,
                'old_messages': old_stats['message_count'],
                'new_messages': new_stats['message_count']
            }

        except Exception as e:
            logger.error(f"❌ Error during manual compaction: {e}")
            return {'success': False, 'error': str(e)}

    def serialize_messages(self, messages: List[ModelMessage]) -> str:
        """Serialize messages to JSON for database storage"""
        try:
            return to_json(messages).decode('utf-8')
        except Exception as e:
            logger.error(f"❌ Error serializing messages: {e}")
            return "[]"
    
    def deserialize_messages(self, json_str: str) -> List[ModelMessage]:
        """Deserialize messages from JSON"""
        try:
            if not json_str:
                return []
            return ModelMessagesTypeAdapter.validate_json(json_str)
        except Exception as e:
            logger.error(f"❌ Error deserializing messages: {e}")
            return []
    
    def clear_conversation(self, conversation_id: str) -> None:
        """Clear conversation context"""
        if conversation_id in self.conversation_contexts:
            del self.conversation_contexts[conversation_id]
        if conversation_id in self.conversation_tokens:
            del self.conversation_tokens[conversation_id]
        logger.info(f"🗑️ Cleared context for conversation: {conversation_id}")
    
    def get_all_conversations(self) -> List[Dict[str, Any]]:
        """Get stats for all active conversations"""
        return [
            self.get_context_stats(conv_id) 
            for conv_id in self.conversation_contexts.keys()
        ]

# Global context manager instance
context_manager = ChatContextManager()

def calculate_token_usage(usage: Optional[Usage]) -> Tuple[int, int, int]:
    """Extract token usage from Pydantic AI Usage object"""
    if not usage:
        return 0, 0, 0
    
    return (
        usage.request_tokens or 0,
        usage.response_tokens or 0, 
        usage.total_tokens or 0
    )

def format_token_display(stats: Dict[str, Any]) -> str:
    """Format token usage for display"""
    tokens = stats['total_tokens']
    max_tokens = stats['max_tokens']
    percentage = stats['token_percentage']
    health = stats['context_health']
    
    # Create visual indicator
    if health == "excellent":
        indicator = "🟢"
    elif health == "good":
        indicator = "🟡"
    elif health == "warning":
        indicator = "🟠"
    else:
        indicator = "🔴"
    
    return f"{indicator} {tokens:,}/{max_tokens:,} tokens ({percentage:.1f}%)"
