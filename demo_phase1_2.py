#!/usr/bin/env python3
"""
Demo script for Phase 1.2: Enhanced Workflow Detection & Planning
Shows off the new enhanced features and toggle functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.unified_agent import get_unified_agent
import asyncio

async def demo_enhanced_planning():
    """Demo the enhanced planning capabilities"""
    agent = get_unified_agent()
    
    print("🎯 **PHASE 1.2 DEMO: Enhanced AI-Powered Planning**\n")
    print("=" * 70)
    
    # Demo 1: Enhanced Website Planning
    print("\n🌟 **Demo 1: Enhanced Website Planning**")
    print("Prompt: 'Create a responsive website for Schnauzers with navigation and gallery'")
    print("-" * 50)
    
    result1 = await agent.process_request(
        "Create a responsive website for Schnauzers with navigation and gallery"
    )
    print(result1)
    
    print("\n" + "=" * 70)
    
    # Demo 2: Toggle Feature
    print("\n🎯 **Demo 2: Manual Planning Toggle**")
    print("Prompt: 'Plan this: Create a simple contact form'")
    print("-" * 50)
    
    result2 = await agent.process_request(
        "Plan this: Create a simple contact form"
    )
    print(result2)
    
    print("\n" + "=" * 70)
    
    # Demo 3: Game Development Workflow
    print("\n🎮 **Demo 3: Game Development Workflow**")
    print("Prompt: 'Create an interactive game for kids'")
    print("-" * 50)
    
    result3 = await agent.process_request(
        "Create an interactive game for kids"
    )
    print(result3)
    
    print("\n" + "=" * 70)
    
    # Demo 4: API Development Workflow
    print("\n🔧 **Demo 4: Backend API Workflow**")
    print("Prompt: 'Build an API for user management with database'")
    print("-" * 50)
    
    result4 = await agent.process_request(
        "Build an API for user management with database"
    )
    print(result4)
    
    print("\n" + "=" * 70)
    print("\n🎉 **PHASE 1.2 DEMO COMPLETE!**")
    print("✅ Enhanced AI-powered workflow detection")
    print("✅ Manual planning toggle feature")
    print("✅ Multiple workflow types (Website, Game, API, App)")
    print("✅ Advanced project requirement analysis")
    print("✅ Beautiful enhanced plan formatting")
    print("✅ Ready for Phase 2: Sequential Action Execution!")

if __name__ == "__main__":
    asyncio.run(demo_enhanced_planning())
