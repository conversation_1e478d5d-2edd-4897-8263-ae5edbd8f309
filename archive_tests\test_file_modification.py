#!/usr/bin/env python3
"""
Test the file modification functionality
"""
import asyncio
import httpx

async def test_file_modification():
    """Test the file modification functionality"""
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            print("🔧 Testing File Modification...")
            
            # Test 1: Modify index.html to change Bean to Chocolate
            print("\n1. Testing file modification (Bean → Chocolate)...")
            response = await client.post(
                'http://localhost:5000/api/unified-ai/process',
                headers={'Content-Type': 'application/json'},
                json={
                    "prompt": "Edit the index.html file to change 'Bean There Café' to 'Chocolate Café' everywhere",
                    "conversation_id": None,
                    "context": {}
                }
            )
            
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"Response: {data.get('response', 'No response')}")
                print(f"Success: {data.get('success', False)}")
            else:
                print(f"Error: {response.text}")
            
            # Test 2: Read the modified file to verify changes
            print("\n2. Reading modified file to verify changes...")
            response = await client.post(
                'http://localhost:5000/api/unified-ai/process',
                headers={'Content-Type': 'application/json'},
                json={
                    "prompt": "Show me the content of index.html",
                    "conversation_id": None,
                    "context": {}
                }
            )
            
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', 'No response')
                print(f"Response length: {len(response_text)} characters")
                # Check if the change was made
                if 'Chocolate Café' in response_text:
                    print("✅ SUCCESS: File was modified correctly!")
                else:
                    print("❌ FAILED: File was not modified correctly")
                print(f"Success: {data.get('success', False)}")
            else:
                print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_file_modification())
