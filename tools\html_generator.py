"""
AI-Powered Code Generator using OpenRouter API
Real AI code generation instead of hard-coded templates (Week 4 Major Improvement)
"""
import re
import logging
import os
import httpx
import json
from typing import List, Optional
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

class CodeGenerationRequest(BaseModel):
    """Request for AI to generate code"""
    file_type: str = Field(description="Type of file to generate (html, css, js, py, etc.)")
    description: str = Field(description="What the code should do or contain")
    style_preferences: Optional[str] = Field(default=None, description="Style preferences (modern, minimal, etc.)")
    additional_requirements: Optional[str] = Field(default=None, description="Any additional requirements")

class GeneratedCode(BaseModel):
    """AI-generated code with metadata"""
    code_content: str = Field(description="The actual generated code")
    explanation: str = Field(description="Brief explanation of what the code does")
    features: List[str] = Field(description="List of features implemented in the code")

class AICodeGenerator:
    """Real AI-powered code generator using OpenRouter API"""

    def __init__(self):
        self.api_key = os.getenv('OPENROUTER_API_KEY')
        self.base_url = 'https://openrouter.ai/api/v1'
        self.model = 'deepseek/deepseek-r1-0528:free'

    async def _call_openrouter_api(self, prompt: str, system_prompt: str) -> str:
        """Call OpenRouter API to generate code"""
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json',
                'HTTP-Referer': 'http://localhost:5000',
                'X-Title': 'AI Code Generator'
            }

            data = {
                'model': self.model,
                'messages': [
                    {'role': 'system', 'content': system_prompt},
                    {'role': 'user', 'content': prompt}
                ],
                'temperature': 0.7,
                'max_tokens': 2000  # Reduced for faster generation
            }

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f'{self.base_url}/chat/completions',
                    headers=headers,
                    json=data,
                    timeout=30.0  # Reduced timeout for faster failure detection
                )

                if response.status_code == 200:
                    result = response.json()
                    return result['choices'][0]['message']['content']
                else:
                    logger.error(f"OpenRouter API error: {response.status_code} - {response.text}")
                    return f"Error: API call failed with status {response.status_code}"

        except Exception as e:
            logger.error(f"Error calling OpenRouter API: {e}")
            return f"Error: {str(e)}"

    async def generate_html_content(self, page_name: str, website_name: str, description: str, style: str = "modern") -> str:
        """Generate HTML content using AI"""
        try:
            system_prompt = """You are an expert HTML developer. Generate complete, valid HTML code based on user requirements.

IMPORTANT RULES:
1. Generate COMPLETE, WORKING HTML code - not templates or placeholders
2. Include proper DOCTYPE, meta tags, and semantic HTML structure
3. Make the HTML responsive and accessible
4. Use modern HTML5 elements and best practices
5. Include inline CSS for styling (no external CSS files)
6. Make the content meaningful and relevant to the request
7. Do NOT use placeholder text like "Your content here" - create real content
8. Include proper head section with title, meta tags, etc.
9. Return ONLY the HTML code, no explanations

Generate professional, production-ready HTML code."""

            prompt = f"""Generate a complete HTML page with these requirements:
- Page name: {page_name}
- Website name: {website_name}
- Description: {description}
- Style: {style}
- Make it a complete, working HTML page
- Include proper head section with title, meta tags
- Use semantic HTML5 elements
- Make it responsive and accessible
- Include inline CSS for styling
- Create meaningful content, not placeholders

Return ONLY the HTML code."""

            result = await self._call_openrouter_api(prompt, system_prompt)
            logger.info(f"✅ AI generated HTML for {page_name}")
            return result

        except Exception as e:
            logger.error(f"❌ Error generating HTML with AI: {e}")
            # Fallback to simple HTML if AI fails
            return self._generate_fallback_html(page_name, website_name, description)

    async def generate_css_content(self, style: str = "modern", description: str = "website styling") -> str:
        """Generate CSS content using AI"""
        try:
            system_prompt = """You are an expert CSS developer. Generate complete, modern CSS code based on user requirements.

IMPORTANT RULES:
1. Generate COMPLETE, WORKING CSS code - not templates or placeholders
2. Use modern CSS features (flexbox, grid, custom properties, etc.)
3. Make the CSS responsive with proper media queries
4. Include proper typography, spacing, and color schemes
5. Use semantic class names and BEM methodology when appropriate
6. Include hover effects, transitions, and modern styling
7. Make the CSS production-ready and well-organized
8. Include comments explaining major sections
9. Use modern CSS reset/normalize approaches
10. Return ONLY the CSS code, no explanations

Generate professional, production-ready CSS code."""

            prompt = f"""Generate complete CSS code with these requirements:
- Style: {style}
- Purpose: {description}
- Make it modern and responsive
- Include proper typography, colors, spacing
- Use flexbox/grid for layouts
- Include hover effects and transitions
- Make it production-ready
- Include media queries for responsiveness

Return ONLY the CSS code."""

            result = await self._call_openrouter_api(prompt, system_prompt)
            logger.info(f"✅ AI generated CSS with {style} style")
            return result

        except Exception as e:
            logger.error(f"❌ Error generating CSS with AI: {e}")
            # Fallback to simple CSS if AI fails
            return self._generate_fallback_css(style)

    async def generate_js_content(self, website_name: str, description: str = "interactive functionality") -> str:
        """Generate JavaScript content using AI"""
        try:
            system_prompt = """You are an expert JavaScript developer. Generate complete, modern JavaScript code based on user requirements.

IMPORTANT RULES:
1. Generate COMPLETE, WORKING JavaScript code - not templates or placeholders
2. Use modern ES6+ syntax (const/let, arrow functions, async/await, etc.)
3. Include proper error handling and validation
4. Make the code modular and well-organized
5. Include comments explaining the functionality
6. Use modern DOM manipulation and event handling
7. Include any necessary utility functions
8. Make the code production-ready and performant
9. Follow JavaScript best practices and conventions
10. Return ONLY the JavaScript code, no explanations

Generate professional, production-ready JavaScript code."""

            prompt = f"""Generate complete JavaScript code with these requirements:
- Website name: {website_name}
- Purpose: {description}
- Use modern ES6+ syntax
- Include DOM manipulation and event handling
- Add interactive features and animations
- Include proper error handling
- Make it production-ready
- Add smooth scrolling, form handling, and UI interactions

Return ONLY the JavaScript code."""

            result = await self._call_openrouter_api(prompt, system_prompt)
            logger.info(f"✅ AI generated JavaScript for {website_name}")
            return result

        except Exception as e:
            logger.error(f"❌ Error generating JavaScript with AI: {e}")
            # Fallback to simple JS if AI fails
            return self._generate_fallback_js(website_name)

    async def generate_python_code(self, description: str, additional_requirements: str = "") -> str:
        """Generate Python code using AI"""
        try:
            system_prompt = """You are an expert Python developer. Generate complete, modern Python code based on user requirements.

IMPORTANT RULES:
1. Generate COMPLETE, WORKING Python code - not templates or placeholders
2. Use modern Python features and best practices
3. Include proper imports, docstrings, and type hints
4. Follow PEP 8 style guidelines
5. Include error handling where appropriate
6. Make the code modular and well-organized
7. Include comments explaining complex logic
8. Use appropriate data structures and algorithms
9. Make the code production-ready and efficient
10. Return ONLY the Python code, no explanations

Generate professional, production-ready Python code."""

            prompt = f"""Generate complete Python code with these requirements:
- Purpose: {description}
- Additional requirements: {additional_requirements}
- Use modern Python features and best practices
- Include proper imports, docstrings, type hints
- Follow PEP 8 style guidelines
- Include error handling where appropriate
- Make it production-ready and well-organized

Return ONLY the Python code."""

            result = await self._call_openrouter_api(prompt, system_prompt)
            logger.info(f"✅ AI generated Python code")
            return result

        except Exception as e:
            logger.error(f"❌ Error generating Python with AI: {e}")
            return f"# Error generating Python code: {e}\n# Please try again with a more specific request"

    async def generate_general_code(self, file_type: str, description: str, additional_requirements: str = "") -> str:
        """Generate code in any language using AI"""
        try:
            system_prompt = f"""You are an expert software developer proficient in {file_type.upper()}. Generate complete, working code based on user requirements.

IMPORTANT RULES:
1. Generate COMPLETE, WORKING {file_type.upper()} code - not templates or placeholders
2. Use modern language features and best practices
3. Include proper syntax, imports, and structure
4. Make the code well-organized and commented
5. Follow {file_type.upper()}-specific conventions and style guides
6. Include error handling where appropriate
7. Make the code production-ready and efficient
8. Provide meaningful variable names and structure
9. Return ONLY the {file_type.upper()} code, no explanations

Generate professional, production-ready {file_type.upper()} code."""

            prompt = f"""Generate complete {file_type.upper()} code with these requirements:
- File type: {file_type}
- Purpose: {description}
- Additional requirements: {additional_requirements}
- Use modern language features and best practices
- Include proper syntax and structure
- Make it production-ready and well-organized
- Follow language-specific conventions

Return ONLY the {file_type.upper()} code."""

            result = await self._call_openrouter_api(prompt, system_prompt)
            logger.info(f"✅ AI generated {file_type} code")
            return result

        except Exception as e:
            logger.error(f"❌ Error generating {file_type} with AI: {e}")
            return f"// Error generating {file_type} code: {e}\n// Please try again with a more specific request"

    def _generate_fallback_html(self, page_name: str, website_name: str, description: str) -> str:
        """Simple fallback HTML if AI generation fails"""
        title = f"{website_name} - {page_name.title()}" if page_name != "index" else website_name
        return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }}
        .container {{ max-width: 800px; margin: 0 auto; }}
        h1 {{ color: #333; }}
        .description {{ background: #f4f4f4; padding: 15px; border-radius: 5px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{website_name}</h1>
        <h2>{page_name.title()}</h2>
        <div class="description">
            <p>{description}</p>
        </div>
        <p>This is a fallback HTML page. The AI generator encountered an issue.</p>
    </div>
</body>
</html>"""

    def _generate_fallback_css(self, style: str) -> str:
        """Simple fallback CSS if AI generation fails"""
        return f"""/* Fallback {style} CSS */
body {{
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    color: #333;
}}

.container {{
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}}

h1, h2, h3 {{
    color: #2c3e50;
}}

.btn {{
    background: #3498db;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}}

.btn:hover {{
    background: #2980b9;
}}"""

    def _generate_fallback_js(self, website_name: str) -> str:
        """Simple fallback JavaScript if AI generation fails"""
        return f"""// Fallback JavaScript for {website_name}
console.log('Welcome to {website_name}!');

// Basic DOM ready function
document.addEventListener('DOMContentLoaded', function() {{
    console.log('Page loaded successfully');

    // Add click handlers to buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {{
        button.addEventListener('click', function(e) {{
            console.log('Button clicked:', this.textContent);
        }});
    }});
}});

// Simple utility functions
function showMessage(message) {{
    alert(message || 'Hello from {website_name}!');
}}"""

# Create global instance
ai_code_generator = AICodeGenerator()

# Legacy wrapper functions for backward compatibility
class HTMLGenerator:
    """Legacy wrapper for backward compatibility - now uses AI generation"""

    @staticmethod
    async def generate_html_content(page_name: str, website_name: str, description: str, style: str) -> str:
        """Generate HTML content using AI"""
        return await ai_code_generator.generate_html_content(page_name, website_name, description, style)

    @staticmethod
    async def generate_css_content(style: str) -> str:
        """Generate CSS content using AI"""
        return await ai_code_generator.generate_css_content(style, f"CSS styling for {style} website")

    @staticmethod
    async def generate_js_content(website_name: str) -> str:
        """Generate JavaScript content using AI"""
        return await ai_code_generator.generate_js_content(website_name, "Interactive website functionality")

    # Old template methods removed - now using AI generation

class HTMLModifier:
    """Handles intelligent modifications to HTML, CSS, and JavaScript files"""

    @staticmethod
    def apply_html_modifications(current_content: str, changes_description: str) -> str:
        """Apply HTML-specific modifications"""
        new_content = current_content
        desc_lower = changes_description.lower()

        # Add sections to HTML
        if any(phrase in desc_lower for phrase in ['add section', 'add more section', 'include section', 'more section']):
            # Find where to insert new sections (before closing body tag)
            if '</body>' in new_content:
                sections_html = HTMLModifier._generate_html_sections(changes_description)
                new_content = new_content.replace('</body>', f'{sections_html}\n</body>')
                logger.info(f"✅ Added HTML sections before </body>")
            else:
                # If no </body> tag, append to end
                sections_html = HTMLModifier._generate_html_sections(changes_description)
                new_content += f'\n{sections_html}'
                logger.info(f"✅ Added HTML sections to end of file")

        # Bean to Chocolate transformations (example transformation)
        elif 'bean' in desc_lower and 'chocolate' in desc_lower:
            modifications = [
                ('Bean There Café', 'Chocolate Café'),
                ('bean', 'chocolate'),
                ('Bean', 'Chocolate'),
                ('coffee beans', 'chocolate bars'),
                ('Coffee Beans', 'Chocolate Bars'),
                ('roasted beans', 'melted chocolate'),
                ('Roasted Beans', 'Melted Chocolate'),
                ('bean-based', 'chocolate-based'),
                ('Bean-based', 'Chocolate-based')
            ]

            for old, new in modifications:
                new_content = new_content.replace(old, new)
            logger.info(f"✅ Applied Bean to Chocolate transformations")

        return new_content
