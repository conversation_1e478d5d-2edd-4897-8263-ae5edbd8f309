#!/usr/bin/env python3
"""
Test the bug fix for workflow execution
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.unified_agent import get_unified_agent

async def test_bug_fix():
    """Test the exact scenario that was failing"""
    print("🔧 **TESTING BUG FIX**\n")
    
    try:
        agent = get_unified_agent()
        print("✅ Agent initialized successfully")
        
        # Test the exact prompt that was failing
        prompt = "Hey! Please create a very creative website, ensure you plan it first"
        print(f"🎯 **Testing Prompt:** {prompt}")
        print("=" * 60)
        
        # This should now work without the 'str' object has no attribute 'file_path' error
        result = await agent.process_request(prompt)
        
        print("📋 **RESULT:**")
        print(result)
        
        # Check if it contains the error
        if "str' object has no attribute 'file_path'" in result:
            print("\n❌ **BUG STILL EXISTS!**")
            return False
        elif "Workflow execution failed" in result:
            print("\n❌ **WORKFLOW EXECUTION FAILED!**")
            return False
        elif "✅" in result and "Created file" in result:
            print("\n🎉 **BUG FIXED! WORKFLOW EXECUTED SUCCESSFULLY!**")
            return True
        else:
            print("\n🤔 **UNEXPECTED RESULT - NEED TO INVESTIGATE**")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run bug fix test"""
    success = await test_bug_fix()
    
    if success:
        print("\n🎉 **BUG FIX SUCCESSFUL!**")
        print("✅ Workflow execution working")
        print("✅ File creation working")
        print("✅ No more 'str' object errors")
        print("✅ System ready for production!")
    else:
        print("\n❌ **BUG FIX FAILED!**")
        print("Need to investigate further.")

if __name__ == "__main__":
    asyncio.run(main())
