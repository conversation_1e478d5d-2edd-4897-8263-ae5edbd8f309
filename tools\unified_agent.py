"""
Unified AI Agent with Proper Pydantic AI Tool Implementation
Uses real Pydantic AI tools that the LLM can intelligently call
Week 4 Improvement: Extracted HTML generation to separate module for better organization
"""
import asyncio
import os
import logging
import re
from typing import Optional, List, Dict, Any, Union
from pathlib import Path
from dataclasses import dataclass
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
from dotenv import load_dotenv

# Removed tool imports - DeepSeek R1 doesn't support tool calling

# Week 4 VITALLY IMPORTANT: Import context management
from tools.context_manager import context_manager, calculate_token_usage, format_token_display

logger = logging.getLogger(__name__)

# Phase 1: Multi-Action Workflow Models (moved here for proper import order)
class WorkflowStep(BaseModel):
    """Individual step in a workflow"""
    step_number: int = Field(description="Order of this step in the workflow")
    action_type: str = Field(description="Type of action (create_file, modify_file, etc.)")
    description: str = Field(description="Human-readable description of what this step does")
    file_path: Optional[str] = Field(default=None, description="File path if this step involves a file")
    estimated_time: Optional[str] = Field(default=None, description="Estimated time for this step")

class WorkflowPlan(BaseModel):
    """Complete workflow plan for multi-step operations"""
    title: str = Field(description="Title of the workflow (e.g., 'Schnauzer Website Creation')")
    description: str = Field(description="Overall description of what will be accomplished")
    total_steps: int = Field(description="Total number of steps in the workflow")
    estimated_duration: str = Field(description="Estimated total time for completion")
    steps: List[WorkflowStep] = Field(description="List of steps to execute")
    requires_workflow: bool = Field(default=True, description="Whether this requires multi-step workflow")

class WorkflowRequest(BaseModel):
    """Request that may require a multi-step workflow"""
    original_prompt: str = Field(description="The original user request")
    requires_workflow: bool = Field(description="Whether this request needs multi-step workflow")
    complexity_score: float = Field(description="Complexity score from 0.0 to 1.0")
    workflow_plan: Optional[WorkflowPlan] = Field(default=None, description="Generated workflow plan if needed")

# Phase 1: Workflow Analysis Engine
class WorkflowAnalyzer:
    """Analyzes requests to determine if multi-step workflows are needed"""

    def __init__(self, model=None):
        self.model = model
        # Keywords that typically indicate multi-step workflows (FLEXIBLE MATCHING)
        self.workflow_indicators = [
            # Website/App Creation (flexible patterns)
            "website", "web site", "web page", "homepage", "site",
            "application", "app", "software", "program",
            "project", "system", "platform",

            # Multi-file Operations
            "multiple", "several", "complete", "full", "entire", "whole",

            # Complex Development Tasks
            "implement", "develop", "build", "create", "make", "design",
            "from scratch", "custom", "professional",

            # Specific Multi-Step Patterns
            "blog", "cms", "dashboard", "admin", "portal", "gallery",
            "responsive", "interactive", "dynamic", "animated",
            "with styling", "with css", "with javascript", "with pages",
            "including", "featuring", "containing"
        ]

        # Single-action indicators (override workflow detection)
        self.single_action_indicators = [
            "show me", "display", "read", "view", "list", "what's in",
            "help", "explain", "how to", "what is", "tell me about",
            "fix this", "debug this", "check this", "review this"
        ]

    async def analyze_request(self, prompt: str) -> WorkflowRequest:
        """Analyze a request to determine if it needs a workflow"""
        prompt_lower = prompt.lower().strip()

        # Check for single-action indicators first
        if any(indicator in prompt_lower for indicator in self.single_action_indicators):
            return WorkflowRequest(
                original_prompt=prompt,
                requires_workflow=False,
                complexity_score=0.1,
                workflow_plan=None
            )

        # Calculate complexity score
        complexity_score = self._calculate_complexity_score(prompt_lower)

        # DEBUG: Log complexity analysis
        logger.info(f"🔍 COMPLEXITY ANALYSIS: '{prompt}' → Score: {complexity_score:.2f}")

        # Determine if workflow is needed (LOWERED THRESHOLD)
        requires_workflow = complexity_score >= 0.4  # Lowered threshold to catch more complex requests

        # Generate workflow plan if needed using AI
        workflow_plan = None
        if requires_workflow:
            logger.info(f"🤖 Generating AI workflow plan for: {prompt}")
            workflow_plan = await self._generate_workflow_plan(prompt)
            if workflow_plan:
                logger.info(f"✅ AI workflow plan generated: {workflow_plan.title}")
            else:
                logger.warning(f"❌ No workflow plan generated")

        return WorkflowRequest(
            original_prompt=prompt,
            requires_workflow=requires_workflow,
            complexity_score=complexity_score,
            workflow_plan=workflow_plan
        )

    def _calculate_complexity_score(self, prompt_lower: str) -> float:
        """Calculate complexity score from 0.0 to 1.0"""
        score = 0.0

        # Base score for workflow indicators
        workflow_matches = sum(1 for indicator in self.workflow_indicators if indicator in prompt_lower)
        score += min(workflow_matches * 0.4, 0.9)  # Increased weight and max

        # Special boost for "create/make/build + website/app" patterns
        if any(create_word in prompt_lower for create_word in ["create", "make", "build"]):
            if any(target_word in prompt_lower for target_word in ["website", "app", "project"]):
                score += 0.3  # Additional boost for these common patterns

        # Additional complexity factors (ENHANCED)
        complexity_factors = [
            # High complexity indicators
            ("blog", 0.3), ("cms", 0.3), ("system", 0.3), ("platform", 0.3),
            ("dashboard", 0.25), ("admin", 0.25), ("portal", 0.25),
            ("multiple", 0.2), ("several", 0.2), ("including", 0.2),

            # Medium complexity indicators
            ("complete", 0.15), ("full", 0.15), ("entire", 0.15),
            ("creative", 0.15), ("custom", 0.15), ("professional", 0.15),
            ("responsive", 0.1), ("interactive", 0.1), ("dynamic", 0.1),

            # Technical indicators
            ("pages", 0.1), ("navigation", 0.1), ("gallery", 0.1),
            ("styling", 0.1), ("css", 0.1), ("javascript", 0.1),
            ("features", 0.1), ("functionality", 0.1), ("components", 0.1)
        ]

        for factor, weight in complexity_factors:
            if factor in prompt_lower:
                score += weight

        # Cap at 1.0
        return min(score, 1.0)

    async def _generate_workflow_plan(self, prompt: str, force_planning: bool = False) -> WorkflowPlan:
        """Generate a workflow plan dynamically using AI - NO HARDCODING"""
        # Let the AI create the plan dynamically
        planning_prompt = f"""
You are an expert project planner. Create a detailed workflow plan for this request: "{prompt}"

Analyze the request and create a step-by-step implementation plan. Consider:
- What files need to be created?
- What is the logical order of implementation?
- How long would each step take?
- What is the complexity level?

Respond with a JSON workflow plan in this exact format:
```json
{{
  "title": "Descriptive title for the workflow",
  "description": "Brief description of what will be accomplished",
  "complexity_level": "basic|intermediate|advanced",
  "estimated_duration": "X-Y minutes",
  "steps": [
    {{
      "step_number": 1,
      "action_type": "create_file|modify_file|analyze",
      "description": "What this step accomplishes",
      "file_path": "filename.ext (if applicable)",
      "estimated_time": "X-Y minutes"
    }}
  ]
}}
```

Be creative and intelligent. Each request is unique - don't use templates!
"""

        try:
            # Use the AI to generate the plan with timeout
            if not self.model:
                logger.warning("No AI model available for dynamic planning, using fallback")
                return self._create_fallback_plan(prompt)

            from pydantic_ai import Agent
            planning_agent = Agent(model=self.model)

            # Add timeout to prevent hanging
            result = await asyncio.wait_for(
                planning_agent.run(planning_prompt),
                timeout=30.0  # 30 second timeout for workflow planning
            )

            # Parse the AI-generated plan
            import json
            import re

            json_match = re.search(r'```json\s*\n(.*?)\n```', result.output, re.DOTALL)
            if json_match:
                plan_data = json.loads(json_match.group(1).strip())

                # Convert to WorkflowPlan
                steps = []
                for step_data in plan_data.get('steps', []):
                    steps.append(WorkflowStep(
                        step_number=step_data['step_number'],
                        action_type=step_data['action_type'],
                        description=step_data['description'],
                        file_path=step_data.get('file_path'),
                        estimated_time=step_data['estimated_time']
                    ))

                return WorkflowPlan(
                    title=plan_data['title'],
                    description=plan_data['description'],
                    total_steps=len(steps),
                    estimated_duration=plan_data['estimated_duration'],
                    steps=steps,
                    requires_workflow=True
                )
            else:
                # Fallback if AI doesn't return proper JSON
                return self._create_fallback_plan(prompt)

        except asyncio.TimeoutError:
            logger.warning(f"⏰ AI workflow plan generation timed out after 30s")
            logger.info(f"📋 Using enhanced fallback plan instead")
            return self._create_enhanced_fallback_plan(prompt)
        except Exception as e:
            logger.error(f"❌ Error generating AI workflow plan: {e}")
            logger.error(f"📋 Using enhanced fallback plan instead")
            return self._create_enhanced_fallback_plan(prompt)

    def _create_fallback_plan(self, prompt: str) -> WorkflowPlan:
        """Create a simple fallback plan if AI generation fails"""
        return WorkflowPlan(
            title="Implementation Plan",
            description=f"Implementation plan for: {prompt}",
            total_steps=2,
            estimated_duration="5-10 minutes",
            steps=[
                WorkflowStep(
                    step_number=1,
                    action_type="analyze",
                    description="Analyze requirements and plan implementation",
                    estimated_time="2-3 minutes"
                ),
                WorkflowStep(
                    step_number=2,
                    action_type="create_file",
                    description="Create the requested implementation",
                    estimated_time="3-7 minutes"
                )
            ],
            requires_workflow=True
        )

    def _create_enhanced_fallback_plan(self, prompt: str) -> WorkflowPlan:
        """Create an enhanced fallback plan with intelligent analysis"""
        prompt_lower = prompt.lower()

        # Intelligent analysis for better fallback plans
        if "website" in prompt_lower:
            return self._create_website_fallback_plan(prompt)
        elif "blog" in prompt_lower:
            return self._create_blog_fallback_plan(prompt)
        elif "app" in prompt_lower or "application" in prompt_lower:
            return self._create_app_fallback_plan(prompt)
        else:
            return self._create_generic_fallback_plan(prompt)

    def _create_website_fallback_plan(self, prompt: str) -> WorkflowPlan:
        """Enhanced fallback plan for website creation"""
        # Extract theme from prompt
        theme = "website"
        if "plant" in prompt.lower():
            theme = "plant website"
        elif "blog" in prompt.lower():
            theme = "website with blog"

        steps = [
            WorkflowStep(
                step_number=1,
                action_type="create_file",
                description=f"Create main HTML structure for {theme}",
                file_path="index.html",
                estimated_time="3-5 minutes"
            ),
            WorkflowStep(
                step_number=2,
                action_type="create_file",
                description="Create modern CSS styling with responsive design",
                file_path="styles.css",
                estimated_time="4-6 minutes"
            ),
            WorkflowStep(
                step_number=3,
                action_type="create_file",
                description="Add interactive JavaScript functionality",
                file_path="script.js",
                estimated_time="3-5 minutes"
            )
        ]

        return WorkflowPlan(
            title=f"{theme.title()} Development",
            description=f"Professional {theme} with modern design and functionality",
            total_steps=len(steps),
            estimated_duration="10-16 minutes",
            steps=steps,
            requires_workflow=True
        )

    def _create_blog_fallback_plan(self, prompt: str) -> WorkflowPlan:
        """Enhanced fallback plan for blog creation"""
        steps = [
            WorkflowStep(
                step_number=1,
                action_type="create_file",
                description="Create blog homepage with post listings",
                file_path="index.html",
                estimated_time="4-6 minutes"
            ),
            WorkflowStep(
                step_number=2,
                action_type="create_file",
                description="Design blog-specific CSS with typography focus",
                file_path="blog.css",
                estimated_time="3-5 minutes"
            ),
            WorkflowStep(
                step_number=3,
                action_type="create_file",
                description="Create sample blog post template",
                file_path="post-template.html",
                estimated_time="2-4 minutes"
            )
        ]

        return WorkflowPlan(
            title="Blog System Development",
            description="Complete blog system with modern design and post management",
            total_steps=len(steps),
            estimated_duration="9-15 minutes",
            steps=steps,
            requires_workflow=True
        )

    def _create_app_fallback_plan(self, prompt: str) -> WorkflowPlan:
        """Enhanced fallback plan for application creation"""
        steps = [
            WorkflowStep(
                step_number=1,
                action_type="create_file",
                description="Create main application structure",
                file_path="app.py",
                estimated_time="5-8 minutes"
            ),
            WorkflowStep(
                step_number=2,
                action_type="create_file",
                description="Set up configuration and dependencies",
                file_path="requirements.txt",
                estimated_time="1-2 minutes"
            )
        ]

        return WorkflowPlan(
            title="Application Development",
            description="Professional application with proper structure and dependencies",
            total_steps=len(steps),
            estimated_duration="6-10 minutes",
            steps=steps,
            requires_workflow=True
        )

    def _create_generic_fallback_plan(self, prompt: str) -> WorkflowPlan:
        """Enhanced generic fallback plan"""
        return WorkflowPlan(
            title="Custom Implementation Plan",
            description=f"Intelligent implementation plan for: {prompt}",
            total_steps=3,
            estimated_duration="8-12 minutes",
            steps=[
                WorkflowStep(
                    step_number=1,
                    action_type="analyze",
                    description="Analyze requirements and create detailed implementation strategy",
                    estimated_time="2-3 minutes"
                ),
                WorkflowStep(
                    step_number=2,
                    action_type="create_file",
                    description="Create main implementation file with core functionality",
                    estimated_time="4-6 minutes"
                ),
                WorkflowStep(
                    step_number=3,
                    action_type="create_file",
                    description="Add documentation and usage instructions",
                    file_path="README.md",
                    estimated_time="2-3 minutes"
                )
            ],
            requires_workflow=True
        )



    # 🎯 TOGGLE FEATURE: Manual Planning Mode
    async def analyze_request_with_toggle(self, prompt: str, force_planning: bool = False) -> WorkflowRequest:
        """Enhanced request analysis with manual planning toggle"""
        prompt_lower = prompt.lower().strip()

        # Check for planning toggle keywords
        planning_triggers = [
            "plan this", "create a plan", "make a plan", "planning mode",
            "show me a plan", "break this down", "step by step plan", "step-by-step plan",
            "workflow for", "implementation plan", "plan for", "plan to"
        ]

        # Auto-detect planning triggers in prompt
        has_planning_trigger = any(trigger in prompt_lower for trigger in planning_triggers)

        # If planning is explicitly requested or forced
        if force_planning or has_planning_trigger:
            logger.info("🎯 Planning mode activated (manual toggle or explicit request)")

            # Remove planning trigger words from prompt for analysis
            clean_prompt = prompt
            for trigger in planning_triggers:
                clean_prompt = clean_prompt.replace(trigger, "").strip()

            # Force workflow generation regardless of complexity using AI
            workflow_plan = await self._generate_workflow_plan(clean_prompt, force_planning=True)

            return WorkflowRequest(
                original_prompt=prompt,
                requires_workflow=True,
                complexity_score=1.0,  # Max score for forced planning
                workflow_plan=workflow_plan
            )

        # Otherwise use normal analysis
        return await self.analyze_request(prompt)

    def _extract_topic_from_prompt(self, prompt: str) -> str:
        """Extract the main topic/theme from the prompt"""
        prompt_lower = prompt.lower()

        # Common patterns for topic extraction
        patterns = [
            r"(?:website|app|project).*?(?:for|about|on)\s+(\w+)",
            r"(?:create|build|make).*?(\w+)\s+(?:website|app|project)",
            r"(\w+)\s+(?:website|app|project)",
        ]

        import re
        for pattern in patterns:
            match = re.search(pattern, prompt_lower)
            if match:
                topic = match.group(1)
                if topic not in ["a", "an", "the", "my", "new", "simple", "basic"]:
                    return topic

        # Fallback: look for capitalized words that might be topics
        words = prompt.split()
        for word in words:
            if word.istitle() and len(word) > 3:
                return word.lower()

        return "custom project"

@dataclass
class AgentDependencies:
    """Dependencies available to all tools"""
    workspace_root: Path
    project_manager: Any = None
    conversation_id: Optional[str] = None
    user_context: Dict[str, Any] = None

    def __post_init__(self):
        if self.user_context is None:
            self.user_context = {}

class FileCreationRequest(BaseModel):
    """Request to create a file"""
    file_path: str = Field(description="Path of the file to create (e.g., 'index.html', 'styles.css')")
    content: str = Field(description="Content to write to the file")
    description: str = Field(description="Brief description of what this file does")

class FileReadRequest(BaseModel):
    """Request to read a file"""
    file_path: str = Field(description="Path of the file to read")

class DirectoryListRequest(BaseModel):
    """Request to list directory contents"""
    directory_path: str = Field(default=".", description="Directory path to list (default: current directory)")

class WebsiteCreationRequest(BaseModel):
    """Request to create a complete website"""
    website_name: str = Field(description="Name/title of the website")
    description: str = Field(description="Description of what the website should contain")
    style: str = Field(default="modern", description="Style of the website (modern, classic, minimal, etc.)")
    pages: List[str] = Field(default=["index"], description="List of pages to create (without .html extension)")

class FileModificationRequest(BaseModel):
    """Request to modify an existing file"""
    file_path: str = Field(description="Path of the file to modify")
    changes_description: str = Field(description="Description of what changes to make to the file")
    new_content: Optional[str] = Field(default=None, description="Complete new content for the file (if provided, replaces entire file)")
    find_and_replace: Optional[List[Dict[str, str]]] = Field(default=None, description="List of find/replace operations: [{'find': 'old text', 'replace': 'new text'}]")

class AICodeGenerationRequest(BaseModel):
    """Request to generate code using AI (Week 4 Major Improvement)"""
    filename: str = Field(description="Name of the file to create (with extension)")
    description: str = Field(description="What the code should do or contain")
    file_type: Optional[str] = Field(default=None, description="Type of file (html, css, js, py, etc.) - auto-detected from filename if not provided")
    additional_requirements: Optional[str] = Field(default=None, description="Any additional requirements or specifications")

class ProjectInfoRequest(BaseModel):
    """Request to get project information"""
    pass  # No parameters needed for project info



class UnifiedAIAgent:
    """Unified AI Agent using proper Pydantic AI tool implementation"""

    def __init__(self):
        # Load environment variables
        load_dotenv()

        # Initialize workspace
        from tools.project_manager import project_manager
        self.project_manager = project_manager
        self.workspace_root = project_manager.get_project_folder()

        # Setup model first
        self.model = self._setup_model()

        # Phase 1: Initialize workflow analyzer with model
        self.workflow_analyzer = WorkflowAnalyzer(self.model)

        # Phase 2: Initialize action queue system
        from tools.action_queue import ActionQueue
        self.action_queue = ActionQueue(self.workspace_root, self.project_manager)

        # Phase 2.5: Workflow context tracking
        self.last_workflow = None
        self.workflow_history = []
        self.created_files = []

        # Create agent with PURE TEXT OUTPUT for DeepSeek R1 (no tool calling, no structured output)
        # This uses manual parsing of text responses instead of any Pydantic AI automation
        self.agent = Agent(
            self.model,
            deps_type=AgentDependencies,
            system_prompt=self._get_system_prompt()
            # NO output_type - pure text generation only
        )

    def _setup_model(self):
        """Setup the AI model"""
        from pydantic_ai.models.openai import OpenAIModel

        openrouter_api_key = os.getenv('OPENROUTER_API_KEY')
        if not openrouter_api_key or openrouter_api_key == 'your-openrouter-api-key':
            logger.error("OpenRouter API key not found. Please set OPENROUTER_API_KEY in .env file")
            raise ValueError("OpenRouter API key not configured")

        # Set environment variable for OpenRouter
        os.environ['OPENROUTER_API_KEY'] = openrouter_api_key

        from pydantic_ai.providers.openrouter import OpenRouterProvider

        return OpenAIModel(
            'deepseek/deepseek-r1-0528:free',  # Use DeepSeek R1 for reasoning capabilities
            provider=OpenRouterProvider(api_key=openrouter_api_key),
        )

    def _get_system_prompt(self) -> str:
        return """You are an intelligent AI assistant powered by DeepSeek R1 with advanced reasoning capabilities.

**IMPORTANT: Response Format**
You can respond in three ways:

1. **Direct text response** - For conversations, explanations, and general questions
2. **JSON action request** - For file operations, wrap your response in ```json``` blocks
3. **Workflow planning** - For complex multi-step requests, create intelligent implementation plans

**File Operations JSON Format:**
```json
{
  "action": "create_file",
  "file_path": "example.py",
  "content": "print('Hello World!')",
  "description": "Simple hello world script"
}
```

```json
{
  "action": "read_file",
  "file_path": "index.html"
}
```

```json
{
  "action": "list_files",
  "directory_path": "."
}
```

```json
{
  "action": "modify_file",
  "file_path": "index.html",
  "changes_description": "Change the title to 'My Website'"
}
```

```json
{
  "action": "project_info"
}
```

**WORKFLOW PLANNING - DYNAMIC AI-POWERED PLANNING:**
When you detect complex requests that need multiple steps (like "create a website", "build an app", "make a game"),
you should intelligently analyze the request and create a custom workflow plan. Each plan should be unique and
tailored to the specific request - NO TEMPLATES OR HARDCODING!

Consider:
- What files are actually needed for this specific request?
- What's the logical implementation order?
- What technologies make sense for this project?
- How complex is this request really?
- What would a professional developer do?

Be creative, intelligent, and adaptive. Every request is different!

**Examples:**
- "Hello there!" → Direct text: "Hello! How can I help you today?"
- "Create a Python script that prints hello world" → JSON action with create_file
- "What's in index.html?" → JSON action with read_file
- "What files do I have?" → JSON action with list_files
- "Edit index.html to change the title" → JSON action with modify_file
- "What's my current project?" → JSON action with project_info
- "Create a website for my dog" → Intelligent workflow planning (analyze what's needed)
- "Build a contact form" → Smart planning (HTML form + CSS styling + maybe JS validation)

**Guidelines:**
- For casual conversation, respond with normal text
- For simple file operations, respond with JSON wrapped in ```json``` code blocks
- For complex multi-step requests, use your intelligence to plan the workflow
- Use your reasoning to determine the user's intent
- Always be helpful and provide clear responses
- Be creative and adaptive - avoid templates and hardcoded responses"""

    async def _handle_structured_output(self, output: str, deps: AgentDependencies) -> str:
        """Parse JSON from text output and execute the requested action"""
        try:
            # Check if output contains JSON action
            if "```json" in output:
                # Extract JSON from code block
                import json
                import re

                json_match = re.search(r'```json\s*\n(.*?)\n```', output, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1).strip()
                    try:
                        action_data = json.loads(json_str)
                        action = action_data.get('action')

                        if action == 'create_file':
                            request = FileCreationRequest(
                                file_path=action_data['file_path'],
                                content=action_data['content'],
                                description=action_data.get('description', 'File created by AI')
                            )
                            return await self._execute_file_creation(request, deps)

                        elif action == 'read_file':
                            request = FileReadRequest(file_path=action_data['file_path'])
                            return await self._execute_file_read(request, deps)

                        elif action == 'list_files':
                            request = DirectoryListRequest(
                                directory_path=action_data.get('directory_path', '.')
                            )
                            return await self._execute_directory_list(request, deps)

                        elif action == 'modify_file':
                            request = FileModificationRequest(
                                file_path=action_data['file_path'],
                                changes_description=action_data['changes_description']
                            )
                            return await self._execute_file_modification(request, deps)

                        elif action == 'project_info':
                            return await self._execute_project_info(deps)

                        else:
                            return f"❌ Unknown action: {action}"

                    except json.JSONDecodeError as e:
                        logger.error(f"JSON parsing error: {e}")
                        return f"❌ Invalid JSON format: {str(e)}"

            # No JSON found - return as direct text response
            return output

        except Exception as e:
            logger.error(f"Error handling structured output: {e}")
            return f"❌ Error executing operation: {str(e)}"

    async def _execute_file_creation(self, request: FileCreationRequest, deps: AgentDependencies) -> str:
        """Execute file creation operation"""
        try:
            if not self._is_safe_path(request.file_path):
                return f"❌ Error: File path '{request.file_path}' is not allowed"

            full_path = deps.workspace_root / request.file_path

            if full_path.exists():
                return f"❌ Error: File '{request.file_path}' already exists."

            # Create parent directories
            full_path.parent.mkdir(parents=True, exist_ok=True)

            # Write file
            full_path.write_text(request.content, encoding='utf-8')

            # Update project file count
            if deps.project_manager:
                deps.project_manager.update_project_file_count()

            file_size = len(request.content.encode('utf-8'))
            return f"✅ Created file '{request.file_path}' ({file_size} bytes)\n📝 {request.description}"

        except Exception as e:
            logger.error(f"Error creating file {request.file_path}: {e}")
            return f"❌ Error creating file '{request.file_path}': {str(e)}"

    async def _execute_file_read(self, request: FileReadRequest, deps: AgentDependencies) -> str:
        """Execute file read operation"""
        try:
            if not self._is_safe_path(request.file_path):
                return f"❌ Error: File path '{request.file_path}' is not allowed"

            full_path = deps.workspace_root / request.file_path

            if not full_path.exists():
                return f"❌ Error: File '{request.file_path}' does not exist"

            content = full_path.read_text(encoding='utf-8')
            file_size = len(content.encode('utf-8'))

            return f"📄 **Content of '{request.file_path}' ({file_size} bytes):**\n\n```\n{content}\n```"

        except Exception as e:
            logger.error(f"Error reading file {request.file_path}: {e}")
            return f"❌ Error reading file '{request.file_path}': {str(e)}"

    async def _execute_directory_list(self, request: DirectoryListRequest, deps: AgentDependencies) -> str:
        """Execute directory listing operation"""
        try:
            if not self._is_safe_path(request.directory_path):
                return f"❌ Error: Directory path '{request.directory_path}' is not allowed"

            full_path = deps.workspace_root / request.directory_path

            if not full_path.exists():
                return f"❌ Error: Directory '{request.directory_path}' does not exist"

            if not full_path.is_dir():
                return f"❌ Error: '{request.directory_path}' is not a directory"

            files = []
            directories = []
            total_size = 0

            for item in full_path.iterdir():
                if item.name.startswith('.'):
                    continue
                if item.is_file():
                    size = item.stat().st_size
                    files.append(f"📄 {item.name} ({size} bytes)")
                    total_size += size
                elif item.is_dir():
                    directories.append(f"📁 {item.name}/")

            if not files and not directories:
                return f"📁 Directory '{request.directory_path}' is empty"

            result = f"📁 **Contents of '{request.directory_path}':**\n\n"

            if directories:
                result += "**Directories:**\n" + "\n".join(sorted(directories)) + "\n\n"

            if files:
                result += "**Files:**\n" + "\n".join(sorted(files)) + "\n\n"
                result += f"📊 **Total files:** {len(files)} ({total_size} bytes)"

            return result

        except Exception as e:
            logger.error(f"Error listing directory {request.directory_path}: {e}")
            return f"❌ Error listing directory '{request.directory_path}': {str(e)}"

    async def _execute_file_modification(self, request: FileModificationRequest, deps: AgentDependencies) -> str:
        """Execute file modification operation"""
        try:
            if not self._is_safe_path(request.file_path):
                return f"❌ Error: File path '{request.file_path}' is not allowed"

            full_path = deps.workspace_root / request.file_path

            if not full_path.exists():
                return f"❌ Error: File '{request.file_path}' does not exist"

            # Read current content
            current_content = full_path.read_text(encoding='utf-8')

            # Apply modifications
            if request.new_content is not None:
                new_content = request.new_content
            elif request.find_and_replace:
                new_content = current_content
                for operation in request.find_and_replace:
                    find_text = operation.get('find', '')
                    replace_text = operation.get('replace', '')
                    new_content = new_content.replace(find_text, replace_text)
            else:
                # Apply intelligent modifications
                new_content = self._apply_intelligent_modifications(current_content, request.changes_description, request.file_path)

            # Write modified content
            full_path.write_text(new_content, encoding='utf-8')

            new_size = len(new_content.encode('utf-8'))
            old_size = len(current_content.encode('utf-8'))
            size_change = new_size - old_size

            return f"✅ **Successfully modified '{request.file_path}'**\n\n" \
                   f"📊 **Size Change:** {old_size} → {new_size} bytes ({size_change:+d})\n" \
                   f"🎯 **Request:** {request.changes_description}"

        except Exception as e:
            logger.error(f"Error modifying file {request.file_path}: {e}")
            return f"❌ Error modifying file '{request.file_path}': {str(e)}"

    async def _execute_project_info(self, deps: AgentDependencies) -> str:
        """Execute project info operation"""
        try:
            if deps.project_manager:
                current_project = deps.project_manager.get_current_project()
                if current_project:
                    return f"📋 **Current Project Information:**\n\n" \
                           f"**Name:** {current_project.name}\n" \
                           f"**Description:** {current_project.description}\n" \
                           f"**Files:** {current_project.file_count}\n" \
                           f"**Created:** {current_project.created_at}\n\n" \
                           f"You can create files, read existing ones, or build new features for this project!"

            return "❌ No active project found. You can create a new project using the project management interface."
        except Exception as e:
            return f"❌ Error getting project info: {str(e)}"
    
    def _is_safe_path(self, file_path: str) -> bool:
        """Check if file path is safe (within workspace) - ENHANCED SECURITY"""
        try:
            if file_path == "." or file_path == "":
                return True

            # SECURITY: Block dangerous characters and patterns
            dangerous_patterns = [
                '@',  # Block @ symbols that might reference external files
                '\\',  # Block Windows path separators
                '//',  # Block network paths
                '~',   # Block home directory references
                '$',   # Block environment variables
                '%',   # Block Windows environment variables
                '`',   # Block command substitution
                '|',   # Block pipe operations
                ';',   # Block command chaining
                '&',   # Block background processes
                '<',   # Block input redirection
                '>',   # Block output redirection
                '*',   # Block wildcards
                '?',   # Block wildcards
                '[',   # Block character classes
                ']',   # Block character classes
                '{',   # Block brace expansion
                '}',   # Block brace expansion
            ]

            for pattern in dangerous_patterns:
                if pattern in file_path:
                    logger.warning(f"🚨 SECURITY: Blocked dangerous pattern '{pattern}' in path: {file_path}")
                    return False

            # SECURITY: Block absolute paths
            if file_path.startswith('/') or (len(file_path) > 1 and file_path[1] == ':'):
                logger.warning(f"🚨 SECURITY: Blocked absolute path: {file_path}")
                return False

            # SECURITY: Block parent directory traversal
            if '..' in file_path:
                logger.warning(f"🚨 SECURITY: Blocked parent directory traversal: {file_path}")
                return False

            # SECURITY: Validate path is within workspace
            full_path = (self.workspace_root / file_path).resolve()
            workspace_resolved = self.workspace_root.resolve()

            if not str(full_path).startswith(str(workspace_resolved)):
                logger.warning(f"🚨 SECURITY: Path outside workspace blocked: {file_path} -> {full_path}")
                return False

            return True
        except Exception as e:
            logger.error(f"🚨 SECURITY: Path validation error for '{file_path}': {e}")
            return False

    # Week 4 Improvement: HTML generation moved to tools/html_generator.py

    # Week 4 Improvement: CSS generation moved to tools/html_generator.py

    # Week 4 Improvement: JavaScript generation moved to tools/html_generator.py

    def _apply_intelligent_modifications(self, current_content: str, changes_description: str, file_path: str) -> str:
        """Apply intelligent modifications to file content based on description - UNIVERSAL FILE SUPPORT"""
        logger.info(f"🔧 Applying intelligent modifications: {changes_description}")
        logger.info(f"📄 File: {file_path}")
        logger.info(f"📝 Current content length: {len(current_content)} chars")

        # Get file extension for context
        file_ext = file_path.split('.')[-1].lower() if '.' in file_path else 'txt'
        logger.info(f"🔍 File type detected: {file_ext}")

        # Apply universal text modifications that work for ANY file type
        new_content = self._apply_universal_text_modifications(current_content, changes_description, file_ext)

        # Apply file-type specific modifications if needed
        if file_path.endswith('.html'):
            new_content = self._apply_html_specific_modifications(new_content, changes_description)
        elif file_path.endswith('.css'):
            new_content = self._apply_css_specific_modifications(new_content, changes_description)
        elif file_path.endswith('.js'):
            new_content = self._apply_js_specific_modifications(new_content, changes_description)
        elif file_path.endswith(('.py', '.md', '.txt', '.json', '.yaml', '.yml', '.xml', '.csv')):
            # These are already handled by universal modifications
            pass

        logger.info(f"✅ Modifications applied. New content length: {len(new_content)} chars")
        return new_content

    def _apply_universal_text_modifications(self, content: str, changes_description: str, file_ext: str) -> str:
        """Apply universal text modifications that work for ANY file type"""
        new_content = content
        desc_lower = changes_description.lower()

        logger.info(f"🌍 Applying universal modifications for .{file_ext} file")

        # 1. APPEND CONTENT - Add content to the end
        if any(phrase in desc_lower for phrase in ['add more', 'write more', 'add content', 'append', 'continue', 'extend']):
            logger.info("📝 Detected append/extend request")

            # Generate additional content based on file type and description
            additional_content = self._generate_additional_content(content, changes_description, file_ext)
            new_content = content + "\n\n" + additional_content
            logger.info(f"✅ Appended {len(additional_content)} characters")

        # 2. REPLACE SPECIFIC TEXT - Find and replace operations
        elif any(phrase in desc_lower for phrase in ['replace', 'change', 'modify', 'update']):
            logger.info("🔄 Detected replace/change request")
            new_content = self._apply_smart_replacements(content, changes_description)

        # 3. INSERT CONTENT - Add content at specific locations
        elif any(phrase in desc_lower for phrase in ['insert', 'add at', 'put in']):
            logger.info("📍 Detected insert request")
            new_content = self._apply_smart_insertions(content, changes_description, file_ext)

        # 4. COUNT/ANALYZE - Just return original content (read-only operation)
        elif any(phrase in desc_lower for phrase in ['count', 'analyze', 'check', 'verify']):
            logger.info("📊 Detected analysis request - no modifications needed")
            # For counting/analysis, we don't modify the content
            return content

        # 5. DEFAULT - Try to intelligently modify based on description
        else:
            logger.info("🧠 Applying intelligent content modification")
            new_content = self._apply_intelligent_content_modification(content, changes_description, file_ext)

        return new_content

    def _generate_additional_content(self, current_content: str, description: str, file_ext: str) -> str:
        """Generate additional content to append to the file"""
        logger.info(f"🎯 Generating additional content for .{file_ext} file")

        # Analyze current content to understand context
        content_preview = current_content[:500] + "..." if len(current_content) > 500 else current_content

        # Generate contextually appropriate content based on file type
        if file_ext == 'md':
            # For Markdown files, continue the story/content
            if 'story' in description.lower() or 'story' in current_content.lower():
                return self._generate_story_continuation(current_content, description)
            else:
                return self._generate_markdown_content(current_content, description)

        elif file_ext == 'py':
            return self._generate_python_continuation(current_content, description)

        elif file_ext == 'txt':
            return self._generate_text_continuation(current_content, description)

        elif file_ext in ['json', 'yaml', 'yml']:
            return self._generate_data_continuation(current_content, description, file_ext)

        else:
            # Generic text continuation
            return self._generate_generic_continuation(current_content, description)

    def _generate_story_continuation(self, current_content: str, description: str) -> str:
        """Generate story continuation for narrative content"""
        logger.info("📚 Generating story continuation")

        # Extract story elements from current content
        lines = current_content.split('\n')
        story_lines = [line for line in lines if line.strip() and not line.startswith('#')]

        if story_lines:
            last_paragraph = story_lines[-1] if story_lines else ""

            # Generate continuation based on the story context
            continuation = f"""
As the days passed, Ferny's adventure continued to unfold in remarkable ways. The little fern had discovered that growth wasn't just about reaching toward the light, but about embracing every challenge that came along the way.

One morning, Ferny noticed something extraordinary happening. New fronds were beginning to unfurl from the center of the plant, each one more vibrant and detailed than the last. These weren't just ordinary leaves - they seemed to shimmer with a subtle iridescence that caught the morning sunlight in the most beautiful way.

The other plants in the garden began to take notice. The wise old oak tree, who had been watching Ferny's progress from afar, finally spoke up. "Young fern," the oak said in a voice like rustling leaves, "you have shown us all what it means to persevere with grace and determination."

Ferny felt a warm glow of pride, but more than that, a deep sense of belonging. This garden wasn't just a place to grow - it was home. And every day brought new opportunities to flourish, to help other plants, and to contribute to the beautiful tapestry of life that surrounded them all.

The story of Ferny the fern was far from over. In fact, it was just beginning to reach its most exciting chapters, filled with friendship, discovery, and the endless possibilities that come with never giving up on your dreams.
"""
            return continuation.strip()

        return "The story continues with new adventures and discoveries..."

    def _apply_smart_replacements(self, content: str, description: str) -> str:
        """Apply smart text replacements based on description"""
        logger.info("🔄 Applying smart replacements")

        new_content = content
        desc_lower = description.lower()

        # Common replacement patterns
        if 'bean' in desc_lower and 'chocolate' in desc_lower:
            replacements = [
                ('bean', 'chocolate'),
                ('Bean', 'Chocolate'),
                ('beans', 'chocolates'),
                ('Beans', 'Chocolates')
            ]
            for old, new in replacements:
                new_content = new_content.replace(old, new)
            logger.info("✅ Applied bean to chocolate replacements")

        return new_content

    def _apply_smart_insertions(self, content: str, description: str, file_ext: str) -> str:
        """Apply smart content insertions"""
        logger.info("📍 Applying smart insertions")

        # For now, append to end (can be enhanced later)
        additional_content = self._generate_additional_content(content, description, file_ext)
        return content + "\n\n" + additional_content

    def _apply_intelligent_content_modification(self, content: str, description: str, file_ext: str) -> str:
        """Apply intelligent content modifications"""
        logger.info("🧠 Applying intelligent content modification")

        # Default behavior: append additional content
        additional_content = self._generate_additional_content(content, description, file_ext)
        return content + "\n\n" + additional_content

    def _generate_markdown_content(self, current_content: str, description: str) -> str:
        """Generate additional markdown content"""
        return f"""
## Additional Content

{description}

This content has been added to extend the document based on your request.
"""

    def _generate_python_continuation(self, current_content: str, description: str) -> str:
        """Generate Python code continuation"""
        return f"""
# Additional Python code based on: {description}

def additional_function():
    \"\"\"Generated function based on modification request\"\"\"
    pass
"""

    def _generate_text_continuation(self, current_content: str, description: str) -> str:
        """Generate plain text continuation"""
        return f"\n\nAdditional content: {description}"

    def _generate_data_continuation(self, current_content: str, description: str, file_ext: str) -> str:
        """Generate data file continuation"""
        if file_ext == 'json':
            return '\n  "additional_field": "Added based on modification request"'
        else:
            return '\nadditional_field: "Added based on modification request"'

    def _generate_generic_continuation(self, current_content: str, description: str) -> str:
        """Generate generic content continuation"""
        return f"\n\n{description}"

    def _apply_html_specific_modifications(self, content: str, description: str) -> str:
        """Apply HTML-specific modifications"""
        try:
            # Import HTMLModifier only when needed to avoid circular imports
            from tools.html_generator import HTMLModifier
            return HTMLModifier.apply_html_modifications(content, description)
        except Exception as e:
            logger.warning(f"⚠️ HTMLModifier not available: {e}")
            return content

    def _apply_css_specific_modifications(self, content: str, description: str) -> str:
        """Apply CSS-specific modifications"""
        try:
            # Import HTMLModifier only when needed to avoid circular imports
            from tools.html_generator import HTMLModifier
            return HTMLModifier.apply_css_modifications(content, description)
        except Exception as e:
            logger.warning(f"⚠️ HTMLModifier not available: {e}")
            return content

    def _apply_js_specific_modifications(self, content: str, description: str) -> str:
        """Apply JavaScript-specific modifications"""
        try:
            # Import HTMLModifier only when needed to avoid circular imports
            from tools.html_generator import HTMLModifier
            return HTMLModifier.apply_js_modifications(content, description)
        except Exception as e:
            logger.warning(f"⚠️ HTMLModifier not available: {e}")
            return content

    def _sanitize_input(self, prompt: str) -> str:
        """Sanitize user input to prevent file access vulnerabilities"""
        # SECURITY: Remove potential file path references with @ symbols
        sanitized = prompt

        # Remove @ file references that might be interpreted as file paths
        import re
        # Remove patterns like @/path/to/file or @file.py
        sanitized = re.sub(r'@[^\s]+', '[REDACTED_FILE_REFERENCE]', sanitized)

        # Log if sanitization occurred
        if sanitized != prompt:
            logger.warning(f"🚨 SECURITY: Input sanitized - removed potential file references")
            logger.info(f"Original length: {len(prompt)}, Sanitized length: {len(sanitized)}")

        return sanitized

    async def process_request(self, prompt: str, conversation_id: Optional[str] = None,
                            user_context: Dict[str, Any] = None) -> str:
        """Process any user request using DeepSeek R1 without tool calling"""
        try:
            # Step 1: SECURITY - Sanitize input
            prompt = self._sanitize_input(prompt)
            logger.info(f"� Processing request with DeepSeek R1: {prompt[:100]}...")

            # Phase 2.5: Check for workflow follow-up requests first
            follow_up_response = self._check_workflow_followup(prompt)
            if follow_up_response:
                return follow_up_response

            # Phase 1.2: Enhanced Workflow Analysis with Toggle Support
            workflow_request = await self.workflow_analyzer.analyze_request_with_toggle(prompt)
            logger.info(f"🔍 Enhanced workflow analysis: requires_workflow={workflow_request.requires_workflow}, "
                       f"complexity_score={workflow_request.complexity_score:.2f}")

            # If workflow is required, execute it using the action queue (Phase 2 implementation)
            if workflow_request.requires_workflow and workflow_request.workflow_plan:
                logger.info(f"📋 Enhanced multi-step workflow detected: {workflow_request.workflow_plan.title}")

                # Phase 2: Execute workflow using action queue
                return await self._execute_workflow_with_queue(workflow_request.workflow_plan)

            # Step 2: Prepare dependencies
            deps = AgentDependencies(
                workspace_root=self.workspace_root,
                project_manager=self.project_manager,
                conversation_id=conversation_id,
                user_context=user_context or {}
            )

            # Step 3: Get conversation context
            if not conversation_id:
                current_project = deps.project_manager.get_current_project() if deps.project_manager else None
                project_id = current_project.id if current_project else None
                conversation_id = context_manager.get_conversation_id(project_id)
                logger.info(f"🆕 Generated conversation ID: {conversation_id}")

            message_history = context_manager.get_message_history(conversation_id)
            context_stats = context_manager.get_context_stats(conversation_id)
            logger.info(f"📊 Context loaded: {len(message_history)} messages, {format_token_display(context_stats)}")

            # Step 4: Execute DeepSeek R1 agent with structured output
            result = await asyncio.wait_for(
                self.agent.run(prompt, deps=deps, message_history=message_history),
                timeout=180.0  # 3 minute timeout
            )
            logger.info(f"✅ DeepSeek R1 completed successfully")

            # Step 5: Handle structured output and execute operations
            final_response = await self._handle_structured_output(result.output, deps)

            # Step 6: Update context with new messages
            context_manager.add_message_to_context(
                conversation_id,
                result.new_messages(),
                result.usage()
            )

            # Log updated context stats
            updated_stats = context_manager.get_context_stats(conversation_id)
            logger.info(f"📊 Context updated: {format_token_display(updated_stats)}")

            return final_response

        except asyncio.TimeoutError:
            logger.warning("⏰ DeepSeek R1 agent timed out")
            return "⏰ **Request timed out** - The operation took too long to complete. Please try a simpler request or break it into smaller parts."
        except Exception as e:
            logger.error(f"❌ Error in DeepSeek R1 agent: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return f"❌ **Error processing request**: {str(e)}\n\nPlease try rephrasing your request or ask for help with a specific task."

    def _analyze_request(self, prompt: str) -> Dict[str, Any]:
        """Analyze the request to determine what actions will be taken"""
        prompt_lower = prompt.lower()
        analysis = {
            'request_type': 'general',
            'target_files': [],
            'operations': [],
            'complexity': 'simple'
        }

        # Detect file operations
        if any(word in prompt_lower for word in ['create', 'make', 'build', 'generate']):
            analysis['request_type'] = 'file_creation'
            analysis['operations'].append('create_files')

        if any(word in prompt_lower for word in ['edit', 'modify', 'change', 'update', 'add']):
            analysis['request_type'] = 'file_modification'
            analysis['operations'].append('modify_files')

        if any(word in prompt_lower for word in ['read', 'show', 'display', 'view']):
            analysis['request_type'] = 'file_reading'
            analysis['operations'].append('read_files')

        if any(word in prompt_lower for word in ['list', 'files', 'directory']):
            analysis['request_type'] = 'file_listing'
            analysis['operations'].append('list_files')

        # Detect target files
        import re
        file_patterns = [
            r'(\w+\.html?)',
            r'(\w+\.css)',
            r'(\w+\.js)',
            r'(\w+\.py)',
            r'(\w+\.json)',
            r'(\w+\.md)',
            r'(\w+\.txt)'
        ]

        for pattern in file_patterns:
            matches = re.findall(pattern, prompt, re.IGNORECASE)
            analysis['target_files'].extend(matches)

        # Determine complexity
        if len(analysis['operations']) > 1 or len(analysis['target_files']) > 1:
            analysis['complexity'] = 'complex'
        elif 'section' in prompt_lower or 'multiple' in prompt_lower:
            analysis['complexity'] = 'moderate'

        return analysis

    def _get_activity_description_from_analysis(self, analysis: Dict[str, Any], prompt: str) -> str:
        """Generate intelligent activity description based on request analysis"""
        request_type = analysis.get('request_type', 'general')
        operations = analysis.get('operations', [])
        target_files = analysis.get('target_files', [])

        # Create contextual descriptions based on analysis
        if request_type == 'file_creation':
            if 'website' in prompt.lower() or len(target_files) > 1:
                return "Creating a complete website with multiple files"
            elif target_files:
                file_type = target_files[0].split('.')[-1] if '.' in target_files[0] else 'file'
                return f"Creating a {file_type.upper()} file"
            else:
                return "Creating new files based on your request"

        elif request_type == 'file_modification':
            if target_files:
                return f"Modifying {', '.join(target_files)}"
            else:
                return "Modifying existing files"

        elif request_type == 'file_reading':
            if target_files:
                return f"Reading {', '.join(target_files)}"
            else:
                return "Reading file contents"

        elif request_type == 'file_listing':
            return "Listing directory contents"

        else:
            # Analyze prompt for more specific activities
            prompt_lower = prompt.lower()

            if any(word in prompt_lower for word in ['code', 'program', 'script', 'function']):
                return "Writing code based on your requirements"
            elif any(word in prompt_lower for word in ['website', 'web', 'html', 'css']):
                return "Building web content"
            elif any(word in prompt_lower for word in ['help', 'explain', 'how', 'what']):
                return "Analyzing your question and preparing a helpful response"
            elif any(word in prompt_lower for word in ['fix', 'debug', 'error', 'problem']):
                return "Debugging and fixing issues"
            else:
                return "Processing your request and preparing a response"

    def _format_result_with_summary(self, result: str, analysis: Dict[str, Any]) -> str:
        """Format the result with an execution summary"""
        summary_parts = []

        # Add operation summary
        if analysis['operations']:
            ops_text = ', '.join(analysis['operations']).replace('_', ' ').title()
            summary_parts.append(f"🔧 **Operations**: {ops_text}")

        # Add target files summary
        if analysis['target_files']:
            files_text = ', '.join(set(analysis['target_files']))
            summary_parts.append(f"📁 **Target Files**: {files_text}")

        # Add complexity indicator
        complexity_icons = {'simple': '🟢', 'moderate': '🟡', 'complex': '🔴'}
        complexity_icon = complexity_icons.get(analysis['complexity'], '🟢')
        summary_parts.append(f"{complexity_icon} **Complexity**: {analysis['complexity'].title()}")

        if summary_parts:
            summary = "📊 **Execution Summary:**\n" + '\n'.join(summary_parts) + "\n\n"
            return summary + result

        return result

    # Phase 1.2: Enhanced Workflow Plan Formatting
    def _format_enhanced_workflow_plan(self, workflow_plan: WorkflowPlan) -> str:
        """Enhanced formatting for workflow plans with better visual design"""
        plan_text = f"🧠 **{workflow_plan.title}**\n\n"

        # Enhanced description with complexity indicator
        complexity_emoji = "🔴" if "advanced" in workflow_plan.description.lower() else "🟡" if "intermediate" in workflow_plan.description.lower() else "🟢"
        plan_text += f"📝 **Description:** {workflow_plan.description} {complexity_emoji}\n\n"

        # Enhanced timing and stats
        plan_text += f"⏱️ **Estimated Duration:** {workflow_plan.estimated_duration}\n"
        plan_text += f"📊 **Total Steps:** {workflow_plan.total_steps}\n"
        plan_text += f"🎯 **Workflow Type:** Multi-Action Sequential Execution\n\n"

        # Enhanced step formatting with progress indicators
        plan_text += "📋 **Detailed Implementation Plan:**\n"
        plan_text += "```\n"
        for i, step in enumerate(workflow_plan.steps):
            # Progress bar visualization
            progress_bar = "█" * (i) + "░" * (len(workflow_plan.steps) - i - 1)
            plan_text += f"[{progress_bar}] Step {step.step_number}\n"
            plan_text += f"    ✨ {step.description}\n"
            if step.file_path:
                plan_text += f"    📁 File: {step.file_path}\n"
            if step.estimated_time:
                plan_text += f"    ⏰ Time: {step.estimated_time}\n"
            plan_text += "\n"
        plan_text += "```\n\n"

        # Enhanced call-to-action
        plan_text += "🚀 **Ready to Execute!**\n"
        plan_text += "This enhanced AI-powered workflow will create each file sequentially with:\n"
        plan_text += "• 🎨 **Custom content** tailored to your requirements\n"
        plan_text += "• 🔧 **Professional code quality** with best practices\n"
        plan_text += "• 📱 **Modern design patterns** and responsive layouts\n"
        plan_text += "• ⚡ **Real-time progress updates** as each step completes\n\n"

        plan_text += "*(Phase 1.2: Enhanced Planning Complete - Sequential execution coming in Phase 2!)*\n\n"

        # Toggle feature explanation
        plan_text += "💡 **Pro Tip:** You can trigger planning mode anytime by saying:\n"
        plan_text += "• \"Plan this for me\"\n"
        plan_text += "• \"Create a plan for...\"\n"
        plan_text += "• \"Show me a step-by-step plan\"\n"
        plan_text += "• \"Break this down into steps\""

        return plan_text

    # Phase 1: Legacy Workflow Plan Formatting (kept for compatibility)
    def _format_workflow_plan(self, workflow_plan: WorkflowPlan) -> str:
        """Format workflow plan for display in chat"""
        plan_text = f"🧠 **{workflow_plan.title}**\n\n"
        plan_text += f"📝 **Description:** {workflow_plan.description}\n\n"
        plan_text += f"⏱️ **Estimated Duration:** {workflow_plan.estimated_duration}\n"
        plan_text += f"📊 **Total Steps:** {workflow_plan.total_steps}\n\n"

        plan_text += "📋 **Implementation Plan:**\n"
        for step in workflow_plan.steps:
            plan_text += f"{step.step_number}. **{step.description}**"
            if step.file_path:
                plan_text += f" → `{step.file_path}`"
            if step.estimated_time:
                plan_text += f" *(~{step.estimated_time})*"
            plan_text += "\n"

        plan_text += "\n🚀 **Let me start building this for you...**\n\n"
        plan_text += "*(Note: This is Phase 1 implementation - currently showing the plan only. "
        plan_text += "Sequential file creation will be implemented in Phase 2.)*"

        return plan_text

    # Phase 2: Workflow Execution with Action Queue
    async def _execute_workflow_with_queue(self, workflow_plan) -> str:
        """Execute workflow using the action queue system"""
        try:
            import time
            from tools.activity_tracker import activity_tracker

            # Start workflow activity tracking
            workflow_id = f"workflow_{int(time.time())}"
            activity_tracker.start_workflow_activity(
                description=f"Executing {workflow_plan.title}",
                workflow_id=workflow_id,
                total_actions=workflow_plan.total_steps
            )

            # Initialize result message
            result_message = f"🚀 **Executing {workflow_plan.title}**\n\n"
            result_message += f"📊 **{workflow_plan.total_steps} actions planned**\n\n"

            # Execute workflow actions
            action_results = []
            async for action_result in self.action_queue.execute_workflow(workflow_plan):
                # Update activity tracker
                activity_tracker.update_workflow_progress(
                    completed_actions=len(action_results) + 1,
                    total_actions=workflow_plan.total_steps,
                    current_action_index=len(action_results)
                )

                # Format action result for display
                action_display = self._format_action_result(action_result)
                action_results.append(action_display)

                logger.info(f"✅ Action completed: {action_result.action_id}")

            # Complete workflow activity tracking
            activity_tracker.complete_workflow_activity(
                workflow_id=workflow_id,
                summary=f"Completed {len(action_results)} actions successfully"
            )

            # Build final result message
            result_message += "📋 **Execution Results:**\n\n"
            for i, action_result in enumerate(action_results, 1):
                result_message += f"**Step {i}:**\n{action_result}\n\n"

            # Add summary
            result_message += f"🎉 **Workflow Complete!**\n"
            result_message += f"✅ Successfully executed {len(action_results)} actions\n"
            result_message += f"⏱️ Total duration: {workflow_plan.estimated_duration}\n"

            # Phase 2.5: Store workflow context for follow-up
            self._store_workflow_context(workflow_plan, action_results)

            return result_message

        except Exception as e:
            logger.error(f"❌ Workflow execution failed: {e}")
            return f"❌ **Workflow execution failed:** {str(e)}\n\nPlease try again or contact support."

    def _format_action_result(self, action_result) -> str:
        """Format action result for display"""
        status_emoji = "✅" if action_result.status.value == "completed" else "❌"

        formatted = f"{status_emoji} **{action_result.action_type.value.replace('_', ' ').title()}**\n"
        formatted += f"   {action_result.result}\n"

        if action_result.file_path:
            formatted += f"   📁 File: `{action_result.file_path}`\n"

        if action_result.execution_time:
            formatted += f"   ⏱️ Time: {action_result.execution_time:.1f}s\n"

        if action_result.error:
            formatted += f"   ❌ Error: {action_result.error}\n"

        return formatted

    # Phase 2.5: Workflow Context Management
    def _check_workflow_followup(self, prompt: str) -> Optional[str]:
        """Check if this is a follow-up to a previous workflow"""
        if not self.last_workflow:
            return None

        prompt_lower = prompt.lower()

        # Follow-up patterns
        followup_patterns = [
            "continue", "step 1", "start at step", "beginning", "from step",
            "add more", "enhance", "improve", "modify", "update",
            "next step", "what's next", "keep going", "proceed",
            "status", "project", "our website", "the website", "current files",
            "what have we", "what did we", "show me", "contact page"
        ]

        if any(pattern in prompt_lower for pattern in followup_patterns):
            logger.info(f"🔄 Detected workflow follow-up request")
            return self._handle_workflow_followup(prompt)

        return None

    def _handle_workflow_followup(self, prompt: str) -> str:
        """Handle follow-up requests to previous workflows"""
        if not self.last_workflow:
            return "❌ No previous workflow found to continue."

        prompt_lower = prompt.lower()

        # Analyze what kind of follow-up this is
        if any(word in prompt_lower for word in ["step 1", "start at step", "beginning", "from step"]):
            return self._provide_workflow_continuation()
        elif any(word in prompt_lower for word in ["add", "enhance", "improve", "modify"]):
            return self._suggest_workflow_enhancements(prompt)
        else:
            return self._provide_workflow_status()

    def _provide_workflow_continuation(self) -> str:
        """Provide continuation options for the last workflow"""
        workflow = self.last_workflow

        response = f"🔄 **Continuing: {workflow['title']}**\n\n"
        response += f"📋 **Previously Completed:**\n"

        for i, file_path in enumerate(workflow['created_files'], 1):
            response += f"✅ Step {i}: Created `{file_path}`\n"

        response += f"\n🚀 **Next Steps You Can Request:**\n"
        response += f"• \"Add more pages to the website\"\n"
        response += f"• \"Create a contact form\"\n"
        response += f"• \"Add a blog section\"\n"
        response += f"• \"Improve the styling\"\n"
        response += f"• \"Add JavaScript animations\"\n"
        response += f"• \"Create a mobile menu\"\n\n"

        response += f"💡 **Or be specific:** \"Add a hero section to index.html\" or \"Create a new about.html page\"\n\n"
        response += f"📁 **Current Files:** {', '.join(workflow['created_files'])}"

        return response

    def _suggest_workflow_enhancements(self, prompt: str) -> str:
        """Suggest enhancements based on the request"""
        workflow = self.last_workflow

        response = f"🔧 **Enhancing: {workflow['title']}**\n\n"
        response += f"📋 **Current Files:** {', '.join(workflow['created_files'])}\n\n"
        response += f"🎯 **Your Request:** {prompt}\n\n"
        response += f"🚀 **I can help you:**\n"
        response += f"• Modify existing files\n"
        response += f"• Add new features\n"
        response += f"• Create additional pages\n"
        response += f"• Improve styling and functionality\n\n"
        response += f"💡 **Please be more specific about what you'd like to add or change!**"

        return response

    def _provide_workflow_status(self) -> str:
        """Provide status of the last workflow"""
        workflow = self.last_workflow

        response = f"📊 **Workflow Status: {workflow['title']}**\n\n"
        response += f"✅ **Completed:** {workflow.get('completed_actions', workflow.get('total_steps', 'Unknown'))} actions\n"
        response += f"📁 **Files Created:** {len(workflow['created_files'])}\n"
        response += f"⏱️ **Duration:** {workflow.get('duration', 'Unknown')}\n\n"

        response += f"📋 **Created Files:**\n"
        for file_path in workflow['created_files']:
            response += f"• `{file_path}`\n"

        response += f"\n🚀 **Ready for next steps!** What would you like to add or modify?"

        return response

    def _store_workflow_context(self, workflow_plan, action_results):
        """Store workflow context for follow-up requests"""
        import time
        created_files = [result.file_path for result in action_results if result.file_path]

        workflow_context = {
            'title': workflow_plan.title,
            'description': workflow_plan.description,
            'total_steps': workflow_plan.total_steps,
            'completed_actions': len(action_results),
            'created_files': created_files,
            'timestamp': time.time()
        }

        self.last_workflow = workflow_context
        self.workflow_history.append(workflow_context)
        self.created_files.extend(created_files)

        logger.info(f"💾 Stored workflow context: {workflow_plan.title}")
        logger.info(f"📁 Created files: {created_files}")

    # Remove old methods - now using proper Pydantic AI tools

    def run_sync(self, prompt: str, conversation_id: Optional[str] = None,
                 user_context: Dict[str, Any] = None) -> str:
        """Synchronous wrapper for process_request"""
        return asyncio.run(self.process_request(prompt, conversation_id, user_context))

# Global instance
unified_agent = None

def get_unified_agent() -> UnifiedAIAgent:
    """Get or create the global unified agent instance"""
    global unified_agent
    if unified_agent is None:
        unified_agent = UnifiedAIAgent()
    return unified_agent
