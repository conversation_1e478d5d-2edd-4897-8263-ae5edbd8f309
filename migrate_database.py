"""
Database Migration Script for Week 4 Context Management
Adds new columns to llm_conversations table for context tracking
"""
import sqlite3
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def migrate_database():
    """Add new context management columns to llm_conversations table"""
    db_path = Path("instance/app.db")
    
    if not db_path.exists():
        logger.info("Database doesn't exist yet, will be created with new schema")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if new columns already exist
        cursor.execute("PRAGMA table_info(llm_conversations)")
        columns = [row[1] for row in cursor.fetchall()]
        
        new_columns = [
            'message_history',
            'request_tokens', 
            'response_tokens',
            'total_tokens',
            'cumulative_tokens',
            'context_length'
        ]
        
        columns_to_add = [col for col in new_columns if col not in columns]
        
        if not columns_to_add:
            logger.info("✅ Database already has all context management columns")
            return
        
        logger.info(f"🔄 Adding {len(columns_to_add)} new columns to llm_conversations table")
        
        # Add new columns
        for column in columns_to_add:
            if column == 'message_history':
                cursor.execute(f"ALTER TABLE llm_conversations ADD COLUMN {column} TEXT")
            else:
                cursor.execute(f"ALTER TABLE llm_conversations ADD COLUMN {column} INTEGER DEFAULT 0")
            logger.info(f"✅ Added column: {column}")
        
        conn.commit()
        logger.info("✅ Database migration completed successfully")
        
    except Exception as e:
        logger.error(f"❌ Database migration failed: {e}")
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    migrate_database()
