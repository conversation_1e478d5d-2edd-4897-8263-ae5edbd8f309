#!/usr/bin/env python3
"""
Debug script to test complexity calculation for the failing prompt
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.unified_agent import WorkflowAnalyzer

async def test_specific_prompt():
    """Test the exact prompt that failed"""
    analyzer = WorkflowAnalyzer()
    
    prompt = "Create a very creative website about plants, including a blog system"
    print(f"🔍 **Testing Prompt:** {prompt}")
    print("-" * 60)
    
    # Manual complexity calculation
    prompt_lower = prompt.lower()
    print(f"**Lowercase:** {prompt_lower}")
    
    # Check workflow indicators
    matches = []
    for indicator in analyzer.workflow_indicators:
        if indicator in prompt_lower:
            matches.append(indicator)
    
    print(f"**Workflow Indicator Matches:** {matches}")
    
    # Check create + website pattern
    has_create = any(word in prompt_lower for word in ["create", "make", "build"])
    has_target = any(word in prompt_lower for word in ["website", "app", "project"])
    print(f"**Create Pattern:** create={has_create}, target={has_target}")
    
    # Check complexity factors
    complexity_factors = [
        # High complexity indicators
        ("blog", 0.3), ("cms", 0.3), ("system", 0.3), ("platform", 0.3),
        ("dashboard", 0.25), ("admin", 0.25), ("portal", 0.25),
        ("multiple", 0.2), ("several", 0.2), ("including", 0.2),
        
        # Medium complexity indicators  
        ("complete", 0.15), ("full", 0.15), ("entire", 0.15),
        ("creative", 0.15), ("custom", 0.15), ("professional", 0.15),
        ("responsive", 0.1), ("interactive", 0.1), ("dynamic", 0.1),
        
        # Technical indicators
        ("pages", 0.1), ("navigation", 0.1), ("gallery", 0.1),
        ("styling", 0.1), ("css", 0.1), ("javascript", 0.1),
        ("features", 0.1), ("functionality", 0.1), ("components", 0.1)
    ]
    
    factor_matches = []
    for factor, weight in complexity_factors:
        if factor in prompt_lower:
            factor_matches.append((factor, weight))
    
    print(f"**Complexity Factor Matches:** {factor_matches}")
    
    # Calculate total score
    base_score = min(len(matches) * 0.4, 0.9)
    create_boost = 0.3 if (has_create and has_target) else 0.0
    factor_score = sum(weight for _, weight in factor_matches)
    total_score = min(base_score + create_boost + factor_score, 1.0)
    
    print(f"**Score Breakdown:**")
    print(f"  Base Score (indicators): {base_score:.2f}")
    print(f"  Create Boost: {create_boost:.2f}")
    print(f"  Factor Score: {factor_score:.2f}")
    print(f"  **TOTAL SCORE: {total_score:.2f}**")
    print(f"  **THRESHOLD: 0.4**")
    print(f"  **WORKFLOW REQUIRED: {total_score >= 0.4}**")
    
    # Test with actual analyzer
    print("\n" + "=" * 60)
    print("**TESTING WITH ACTUAL ANALYZER:**")
    
    try:
        result = await analyzer.analyze_request(prompt)
        print(f"**Actual Score:** {result.complexity_score:.2f}")
        print(f"**Actual Workflow Required:** {result.requires_workflow}")
        
        if result.workflow_plan:
            print(f"**Plan Generated:** {result.workflow_plan.title}")
        else:
            print("**No Plan Generated**")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_specific_prompt())
