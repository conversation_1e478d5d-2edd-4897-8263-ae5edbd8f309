#!/usr/bin/env python3
"""
Simple test for AI coding agent with OpenRouter
"""
import asyncio

async def test_simple_ai_coder():
    """Test basic AI coding agent functionality"""
    try:
        print("🧪 Testing AI Coding Agent with OpenRouter...")
        
        # Test 1: Basic import
        print("📦 Importing AI coding agent...")
        from tools.cursor_agent import AICodingAgent
        print("✅ Import successful")

        # Test 2: Agent initialization
        print("🤖 Initializing agent...")
        agent = AICodingAgent()
        print("✅ Agent initialized")
        
        # Test 3: Simple request (fallback mode)
        print("💬 Testing simple request...")
        result = await agent.process_request("Say hello")
        print(f"✅ Response: {result[:100]}...")
        
        # Test 4: File creation request
        print("📝 Testing file creation...")
        result = await agent.process_request("Create a simple HTML file called test.html")
        print(f"✅ File creation response: {result[:200]}...")
        
        print("\n🎉 All tests passed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_simple_ai_coder())
