#!/usr/bin/env python3
"""
Test just the action queue system directly
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_action_queue_directly():
    """Test the action queue directly"""
    print("🔧 **TESTING ACTION QUEUE DIRECTLY**\n")
    
    try:
        from tools.action_queue import ActionQueue, WorkflowAction, ActionType
        from tools.unified_agent import WorkflowPlan, WorkflowStep
        from pathlib import Path
        
        # Create action queue
        workspace = Path("test_workspace")
        workspace.mkdir(exist_ok=True)
        
        queue = ActionQueue(workspace)
        print("✅ Action queue created")
        
        # Create a mock workflow plan
        workflow_plan = WorkflowPlan(
            title="Test Coffee Shop Website",
            description="Simple website for testing action queue",
            total_steps=3,
            estimated_duration="5-7 minutes",
            steps=[
                WorkflowStep(
                    step_number=1,
                    action_type="create_file",
                    description="Create HTML homepage for coffee shop",
                    file_path="index.html",
                    estimated_time="2-3 minutes"
                ),
                WorkflowStep(
                    step_number=2,
                    action_type="create_file", 
                    description="Create CSS styling for modern design",
                    file_path="styles.css",
                    estimated_time="2-3 minutes"
                ),
                WorkflowStep(
                    step_number=3,
                    action_type="analyze",
                    description="Analyze project structure and provide summary",
                    estimated_time="1 minute"
                )
            ],
            requires_workflow=True
        )
        
        print(f"📋 Created workflow plan: {workflow_plan.title}")
        print(f"📊 Total steps: {workflow_plan.total_steps}")
        
        print("\n🚀 Executing workflow...")
        
        # Execute workflow
        results = []
        async for action_result in queue.execute_workflow(workflow_plan):
            print(f"✅ Action completed: {action_result.action_id}")
            print(f"   Type: {action_result.action_type.value}")
            print(f"   Status: {action_result.status.value}")
            print(f"   Result: {action_result.result}")
            if action_result.file_path:
                print(f"   File: {action_result.file_path}")
            if action_result.execution_time:
                print(f"   Time: {action_result.execution_time:.2f}s")
            print()
            results.append(action_result)
        
        print(f"🎉 Workflow execution complete! {len(results)} actions executed.")
        
        # Check final queue status
        final_status = queue.get_queue_status()
        print(f"📊 Final Queue Status:")
        print(f"   Total actions: {final_status['total_actions']}")
        print(f"   Completed: {final_status['completed_actions']}")
        print(f"   Failed: {final_status['failed_actions']}")
        print(f"   Progress: {final_status['progress_percentage']:.1f}%")
        
        # Check created files
        print("\n📁 **Created Files:**")
        for file_path in workspace.glob("*"):
            if file_path.is_file():
                size = file_path.stat().st_size
                print(f"   {file_path.name} ({size} bytes)")
                
                # Show file content preview
                content = file_path.read_text(encoding='utf-8')
                preview = content[:200] + "..." if len(content) > 200 else content
                print(f"      Preview: {preview}")
                print()
        
        # Cleanup
        import shutil
        shutil.rmtree(workspace, ignore_errors=True)
        print("🗑️ Cleaned up test workspace")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct queue test error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run action queue test"""
    success = await test_action_queue_directly()
    
    if success:
        print("\n🎉 **ACTION QUEUE TEST SUCCESSFUL!**")
        print("✅ Action Queue System working perfectly")
        print("✅ Multi-action workflow execution implemented")
        print("✅ File creation and management working")
        print("✅ Progress tracking and status updates working")
        print("✅ Ready for Phase 3: Streaming Action Execution!")
    else:
        print("\n❌ **ACTION QUEUE TEST FAILED!**")
        print("Need to debug and fix issues before proceeding.")

if __name__ == "__main__":
    asyncio.run(main())
