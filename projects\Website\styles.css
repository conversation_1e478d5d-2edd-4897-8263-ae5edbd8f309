```css
/* Modern CSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
  overflow-x: hidden;
}

/* Color Variables */
:root {
  --primary: #4361ee;
  --primary-dark: #3a56d4;
  --secondary: #7209b7;
  --accent: #4cc9f0;
  --light: #f8f9fa;
  --dark: #212529;
  --gray: #6c757d;
  --success: #4caf50;
  --warning: #ff9800;
  --error: #f44336;
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
  --border-radius: 12px;
  --section-spacing: 5rem;
  --container-width: 1200px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: var(--dark);
}

h1 {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
}

h2 {
  font-size: clamp(2rem, 4vw, 2.8rem);
  position: relative;
  display: inline-block;
  margin-bottom: 2.5rem;
}

h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 4px;
  background: var(--primary);
  border-radius: 2px;
}

h3 {
  font-size: clamp(1.5rem, 3vw, 1.8rem);
}

p {
  margin-bottom: 1rem;
  color: var(--gray);
}

a {
  text-decoration: none;
  color: var(--primary);
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
}

/* Container */
.container {
  width: 90%;
  max-width: var(--container-width);
  margin: 0 auto;
  padding: 0 1rem;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.btn {
  display: inline-block;
  padding: 0.8rem 2rem;
  background-color: var(--primary);
  color: white;
  border-radius: var(--border-radius);
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: var(--transition);
  text-align: center;
}

.btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: var(--shadow);
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--primary);
  color: var(--primary);
}

.btn-outline:hover {
  background: var(--primary);
  color: white;
}

.section {
  padding: var(--section-spacing) 0;
}

.section-title {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title h2::after {
  left: 50%;
  transform: translateX(-50%);
}

/* Header */
.header {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow);
  padding: 1rem 0;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.8rem;
  font-weight: 800;
  color: var(--primary);
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-link {
  font-weight: 600;
  color: var(--dark);
  position: relative;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary);
  transition: var(--transition);
}

.nav-link:hover::after {
  width: 100%;
}

.mobile-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--dark);
}

/* Hero Section */
.hero {
  padding-top: 7rem;
  padding-bottom: 5rem;
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.1) 0%, rgba(114, 9, 183, 0.1) 100%);
  z-index: -1;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-text h1 {
  margin-bottom: 1.5rem;
}

.hero-text p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.hero-btns {
  display: flex;
  gap: 1rem;
}

.hero-image {
  position: relative;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
}

.hero-image img {
  width: 100%;
  height: auto;
  display: block;
}

/* Features Section */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2.5rem;
  margin-top: 3rem;
}

.feature-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow);
  transition: var(--transition);
  text-align: center;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 2.5rem;
  color: var(--primary);
  margin-bottom: 1.5rem;
}

/* Testimonials Section */
.testimonials {
  background: linear-gradient(to right, #f0f4ff, #e6f7ff);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.testimonial-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow);
  position: relative;
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: 1rem;
  left: 1.5rem;
  font-size: 5rem;
  color: var(--primary);
  opacity: 