"""
LLM interaction tools with OpenRouter integration
"""
import time
import uuid
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, field_validator
import httpx
from flask import current_app
import logging

logger = logging.getLogger(__name__)

# Pydantic models for LLM operations
class LLMMessage(BaseModel):
    role: str = Field(..., description="Message role (user, assistant, system)")
    content: str = Field(..., description="Message content")
    
    @field_validator('role')
    @classmethod
    def validate_role(cls, v):
        allowed_roles = {'user', 'assistant', 'system'}
        if v not in allowed_roles:
            raise ValueError(f"Role must be one of: {allowed_roles}")
        return v

class AttachedFile(BaseModel):
    id: int = Field(..., description="File ID")
    filename: str = Field(..., description="Original filename")
    size: int = Field(..., description="File size in bytes")
    mime_type: Optional[str] = Field(None, description="MIME type")
    hash: str = Field(..., description="File hash")

class LLMRequest(BaseModel):
    prompt: str = Field(..., description="Prompt to send to LLM")
    model: str = Field(default="deepseek/deepseek-r1-0528:free", description="Model to use")
    messages: Optional[List[LLMMessage]] = Field(None, description="Conversation history")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens to generate")
    temperature: float = Field(default=0.7, description="Sampling temperature")
    top_p: float = Field(default=1.0, description="Top-p sampling")
    stream: bool = Field(default=False, description="Stream response")
    conversation_id: Optional[str] = Field(None, description="Conversation ID for tracking")
    attached_files: Optional[List[AttachedFile]] = Field(None, description="Attached files")
    
    @field_validator('temperature')
    @classmethod
    def validate_temperature(cls, v):
        if not 0.0 <= v <= 2.0:
            raise ValueError("Temperature must be between 0.0 and 2.0")
        return v
    
    @field_validator('top_p')
    @classmethod
    def validate_top_p(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError("Top-p must be between 0.0 and 1.0")
        return v

class LLMResponse(BaseModel):
    response: str = Field(..., description="LLM response")
    model: str = Field(..., description="Model used")
    conversation_id: str = Field(..., description="Conversation ID")
    tokens_used: Optional[int] = Field(None, description="Total tokens used")
    prompt_tokens: Optional[int] = Field(None, description="Prompt tokens")
    completion_tokens: Optional[int] = Field(None, description="Completion tokens")
    cost: Optional[float] = Field(None, description="Cost in USD")
    response_time: float = Field(..., description="Response time in seconds")
    finish_reason: Optional[str] = Field(None, description="Reason for completion")
    reasoning_content: Optional[str] = Field(None, description="DeepSeek R1 reasoning content (Chain of Thought)")

class ModelInfo(BaseModel):
    id: str = Field(..., description="Model ID")
    name: str = Field(..., description="Model name")
    description: Optional[str] = Field(None, description="Model description")
    context_length: Optional[int] = Field(None, description="Maximum context length")
    pricing: Optional[Dict[str, float]] = Field(None, description="Pricing information")

class ModelsListResponse(BaseModel):
    models: List[ModelInfo] = Field(..., description="Available models")
    total_count: int = Field(..., description="Total number of models")

class ConversationSummary(BaseModel):
    conversation_id: str = Field(..., description="Conversation ID")
    message_count: int = Field(..., description="Number of messages")
    total_tokens: int = Field(..., description="Total tokens used")
    total_cost: float = Field(..., description="Total cost")
    created_at: str = Field(..., description="Creation timestamp")
    last_message_at: str = Field(..., description="Last message timestamp")

class ConversationsListResponse(BaseModel):
    conversations: List[ConversationSummary] = Field(..., description="Conversation summaries")
    total_count: int = Field(..., description="Total number of conversations")

class LLMTools:
    """LLM interaction tools with OpenRouter integration"""
    
    @staticmethod
    def _get_api_config():
        """Get API configuration from Flask app"""
        if not hasattr(current_app, 'config'):
            raise ValueError("Flask app context required")
        
        api_key = current_app.config.get('OPENROUTER_API_KEY')
        base_url = current_app.config.get('OPENROUTER_BASE_URL', 'https://openrouter.ai/api/v1')
        
        if not api_key:
            raise ValueError("OPENROUTER_API_KEY not configured")
        
        return api_key, base_url
    
    @staticmethod
    def _calculate_cost(usage: Dict[str, Any], model: str) -> Optional[float]:
        """Calculate cost based on token usage (simplified)"""
        # This is a simplified cost calculation
        # In a real implementation, you'd have a pricing table for different models
        prompt_tokens = usage.get('prompt_tokens', 0)
        completion_tokens = usage.get('completion_tokens', 0)
        
        # Example pricing (per 1K tokens) - adjust based on actual OpenRouter pricing
        pricing_map = {
            'deepseek/deepseek-r1-0528:free': {'prompt': 0.0, 'completion': 0.0},  # Free model!
            'deepseek/deepseek-chat': {'prompt': 0.00014, 'completion': 0.00028},
            'openai/gpt-3.5-turbo': {'prompt': 0.0015, 'completion': 0.002},
            'openai/gpt-4': {'prompt': 0.03, 'completion': 0.06},
        }
        
        if model not in pricing_map:
            return None
        
        pricing = pricing_map[model]
        cost = (prompt_tokens / 1000 * pricing['prompt'] + 
                completion_tokens / 1000 * pricing['completion'])
        
        return round(cost, 6)

    @classmethod
    def _prepare_file_context(cls, attached_files: List[AttachedFile]) -> str:
        """Prepare file context for the LLM prompt"""
        try:
            from models import db, File

            file_contexts = []
            for attached_file in attached_files:
                # Get file from database
                file_record = db.session.execute(
                    db.select(File).filter_by(id=attached_file.id)
                ).scalar_one_or_none()

                if file_record:
                    file_contexts.append(f"""
File: {attached_file.filename}
Size: {attached_file.size} bytes
Type: {attached_file.mime_type or 'unknown'}
Content:
```
{file_record.content[:5000]}{'...' if len(file_record.content) > 5000 else ''}
```
""")

            if file_contexts:
                return "I have attached the following files for your analysis:\n" + "\n".join(file_contexts)

            return ""

        except Exception as e:
            logger.error(f"Error preparing file context: {e}")
            return "Error: Could not load attached files."
    
    @classmethod
    async def query_llm(cls, request: LLMRequest) -> LLMResponse:
        """Send query to LLM and get response"""
        start_time = time.time()
        
        try:
            api_key, base_url = cls._get_api_config()
            
            # Generate conversation ID if not provided
            conversation_id = request.conversation_id or str(uuid.uuid4())
            
            # Prepare messages
            messages = []
            if request.messages:
                messages = [{"role": msg.role, "content": msg.content} for msg in request.messages]

            # Prepare the current prompt, including attached files
            current_prompt = request.prompt
            if request.attached_files:
                file_context = cls._prepare_file_context(request.attached_files)
                if file_context:
                    current_prompt = f"{file_context}\n\n{request.prompt}"

            # Add the current prompt as a user message
            messages.append({"role": "user", "content": current_prompt})
            
            # Prepare request payload
            payload = {
                "model": request.model,
                "messages": messages,
                "temperature": request.temperature,
                "top_p": request.top_p,
                "stream": request.stream
            }
            
            if request.max_tokens:
                payload["max_tokens"] = request.max_tokens
            
            # Make API request
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    f"{base_url}/chat/completions",
                    headers={
                        "Authorization": f"Bearer {api_key}",
                        "Content-Type": "application/json",
                        "HTTP-Referer": "http://localhost:5000",  # Required by OpenRouter
                        "X-Title": "Flask AI App"  # Optional but recommended
                    },
                    json=payload
                )
                
                response.raise_for_status()
                result = response.json()
            
            # Extract response data
            choice = result['choices'][0]
            message = choice['message']
            message_content = message['content']
            finish_reason = choice.get('finish_reason')

            # Extract DeepSeek R1 reasoning content if available
            reasoning_content = message.get('reasoning_content')
            if reasoning_content:
                logger.info(f"🧠 DeepSeek R1 reasoning captured: {len(reasoning_content)} chars")
            
            # Extract usage information
            usage = result.get('usage', {})
            total_tokens = usage.get('total_tokens')
            prompt_tokens = usage.get('prompt_tokens')
            completion_tokens = usage.get('completion_tokens')
            
            # Calculate cost
            cost = cls._calculate_cost(usage, request.model)
            
            response_time = time.time() - start_time

            # Save conversation to database
            try:
                cls._save_conversation_to_db(
                    conversation_id=conversation_id,
                    model=request.model,
                    prompt=request.prompt,
                    response=message_content,
                    tokens_used=total_tokens,
                    cost=cost,
                    response_time=response_time
                )
            except Exception as e:
                logger.warning(f"Failed to save conversation to database: {e}")

            logger.info(f"LLM query completed: {request.model}, {total_tokens} tokens, {response_time:.2f}s")

            return LLMResponse(
                response=message_content,
                model=request.model,
                conversation_id=conversation_id,
                tokens_used=total_tokens,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                cost=cost,
                response_time=response_time,
                finish_reason=finish_reason,
                reasoning_content=reasoning_content
            )
            
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error querying LLM: {e.response.status_code} - {e.response.text}")
            raise ValueError(f"LLM API error: {e.response.status_code}")
        except Exception as e:
            logger.error(f"Error querying LLM: {e}")
            raise
    
    @classmethod
    async def get_available_models(cls) -> ModelsListResponse:
        """Get list of available models from OpenRouter"""
        try:
            api_key, base_url = cls._get_api_config()
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(
                    f"{base_url}/models",
                    headers={
                        "Authorization": f"Bearer {api_key}",
                        "Content-Type": "application/json"
                    }
                )
                
                response.raise_for_status()
                result = response.json()
            
            # Parse models data
            models = []
            for model_data in result.get('data', []):
                model_info = ModelInfo(
                    id=model_data['id'],
                    name=model_data.get('name', model_data['id']),
                    description=model_data.get('description'),
                    context_length=model_data.get('context_length'),
                    pricing=model_data.get('pricing')
                )
                models.append(model_info)
            
            logger.info(f"Retrieved {len(models)} available models")
            
            return ModelsListResponse(
                models=models,
                total_count=len(models)
            )
            
        except Exception as e:
            logger.error(f"Error getting available models: {e}")
            raise
    
    @classmethod
    async def stream_llm_response(cls, request: LLMRequest):
        """Stream LLM response (generator function)"""
        try:
            api_key, base_url = cls._get_api_config()

            # Set stream to True
            request.stream = True

            # Prepare messages
            messages = []
            if request.messages:
                messages = [{"role": msg.role, "content": msg.content} for msg in request.messages]

            messages.append({"role": "user", "content": request.prompt})

            # Prepare request payload
            payload = {
                "model": request.model,
                "messages": messages,
                "temperature": request.temperature,
                "top_p": request.top_p,
                "stream": True
            }

            if request.max_tokens:
                payload["max_tokens"] = request.max_tokens

            # Make streaming API request
            async with httpx.AsyncClient(timeout=60.0) as client:
                async with client.stream(
                    "POST",
                    f"{base_url}/chat/completions",
                    headers={
                        "Authorization": f"Bearer {api_key}",
                        "Content-Type": "application/json",
                        "HTTP-Referer": "http://localhost:5000",
                        "X-Title": "Flask AI App"
                    },
                    json=payload
                ) as response:
                    response.raise_for_status()

                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]  # Remove "data: " prefix
                            if data == "[DONE]":
                                break

                            try:
                                import json
                                chunk = json.loads(data)
                                if 'choices' in chunk and chunk['choices']:
                                    delta = chunk['choices'][0].get('delta', {})
                                    if 'content' in delta:
                                        yield delta['content']
                            except json.JSONDecodeError:
                                continue

        except Exception as e:
            logger.error(f"Error streaming LLM response: {e}")
            raise

    @classmethod
    async def stream_llm_with_reasoning(cls, request: LLMRequest):
        """Stream LLM response with DeepSeek R1 reasoning content (enhanced generator)"""
        try:
            api_key, base_url = cls._get_api_config()

            # Set stream to True
            request.stream = True

            # Prepare messages
            messages = []
            if request.messages:
                messages = [{"role": msg.role, "content": msg.content} for msg in request.messages]

            # Prepare the current prompt, including attached files
            current_prompt = request.prompt
            if request.attached_files:
                file_context = cls._prepare_file_context(request.attached_files)
                if file_context:
                    current_prompt = f"{file_context}\n\n{request.prompt}"

            messages.append({"role": "user", "content": current_prompt})

            # Prepare request payload
            payload = {
                "model": request.model,
                "messages": messages,
                "temperature": request.temperature,
                "top_p": request.top_p,
                "stream": True
            }

            if request.max_tokens:
                payload["max_tokens"] = request.max_tokens

            # Track reasoning and content separately
            reasoning_buffer = ""
            content_buffer = ""
            is_reasoning_phase = True

            # Make streaming API request
            async with httpx.AsyncClient(timeout=60.0) as client:
                async with client.stream(
                    "POST",
                    f"{base_url}/chat/completions",
                    headers={
                        "Authorization": f"Bearer {api_key}",
                        "Content-Type": "application/json",
                        "HTTP-Referer": "http://localhost:5000",
                        "X-Title": "Flask AI App"
                    },
                    json=payload
                ) as response:
                    response.raise_for_status()

                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]  # Remove "data: " prefix
                            if data == "[DONE]":
                                break

                            try:
                                import json
                                chunk = json.loads(data)
                                if 'choices' in chunk and chunk['choices']:
                                    delta = chunk['choices'][0].get('delta', {})

                                    # Handle DeepSeek R1 reasoning content
                                    if 'reasoning_content' in delta:
                                        reasoning_delta = delta['reasoning_content']
                                        reasoning_buffer += reasoning_delta

                                        # Yield reasoning update with progress
                                        yield {
                                            'type': 'reasoning',
                                            'content': reasoning_delta,
                                            'full_reasoning': reasoning_buffer,
                                            'progress': 0.3  # Reasoning phase is ~30% of total
                                        }

                                    # Handle regular content
                                    elif 'content' in delta:
                                        content_delta = delta['content']
                                        content_buffer += content_delta
                                        is_reasoning_phase = False

                                        # Yield content update with progress
                                        progress = 0.3 + (len(content_buffer) / max(len(content_buffer) + 100, 100)) * 0.7
                                        yield {
                                            'type': 'content',
                                            'content': content_delta,
                                            'full_content': content_buffer,
                                            'progress': min(progress, 1.0)
                                        }

                            except json.JSONDecodeError:
                                continue

        except Exception as e:
            logger.error(f"Error streaming LLM response with reasoning: {e}")
            raise

    @staticmethod
    def _save_conversation_to_db(conversation_id: str, model: str, prompt: str,
                                response: str, tokens_used: Optional[int] = None,
                                cost: Optional[float] = None, response_time: Optional[float] = None):
        """Save conversation to database"""
        try:
            from models import db, LLMConversation

            conversation = LLMConversation(
                conversation_id=conversation_id,
                model=model,
                prompt=prompt,
                response=response,
                tokens_used=tokens_used,
                cost=cost,
                response_time=response_time
            )

            db.session.add(conversation)
            db.session.commit()

            logger.debug(f"Saved conversation {conversation_id} to database")

        except Exception as e:
            logger.error(f"Error saving conversation to database: {e}")
            db.session.rollback()
            raise

    @classmethod
    async def get_conversation_history(cls, conversation_id: str) -> List[LLMMessage]:
        """Get conversation history from database"""
        try:
            from models import db, LLMConversation

            conversations = db.session.execute(
                db.select(LLMConversation)
                .filter_by(conversation_id=conversation_id)
                .order_by(LLMConversation.created_at)
            ).scalars().all()

            messages = []
            for conv in conversations:
                # Add user message
                messages.append(LLMMessage(role="user", content=conv.prompt))
                # Add assistant response
                messages.append(LLMMessage(role="assistant", content=conv.response))

            return messages

        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []

    @classmethod
    async def get_conversations_list(cls, limit: int = 50) -> ConversationsListResponse:
        """Get list of recent conversations"""
        try:
            from models import db, LLMConversation
            from sqlalchemy import func

            # Get conversation summaries
            conversations_query = db.session.execute(
                db.select(
                    LLMConversation.conversation_id,
                    func.count(LLMConversation.id).label('message_count'),
                    func.sum(LLMConversation.tokens_used).label('total_tokens'),
                    func.sum(LLMConversation.cost).label('total_cost'),
                    func.min(LLMConversation.created_at).label('created_at'),
                    func.max(LLMConversation.created_at).label('last_message_at')
                )
                .group_by(LLMConversation.conversation_id)
                .order_by(func.max(LLMConversation.created_at).desc())
                .limit(limit)
            ).all()

            conversations = []
            for row in conversations_query:
                summary = ConversationSummary(
                    conversation_id=row.conversation_id,
                    message_count=row.message_count or 0,
                    total_tokens=int(row.total_tokens or 0),
                    total_cost=float(row.total_cost or 0.0),
                    created_at=row.created_at.isoformat(),
                    last_message_at=row.last_message_at.isoformat()
                )
                conversations.append(summary)

            return ConversationsListResponse(
                conversations=conversations,
                total_count=len(conversations)
            )

        except Exception as e:
            logger.error(f"Error getting conversations list: {e}")
            return ConversationsListResponse(conversations=[], total_count=0)

    @classmethod
    async def delete_conversation(cls, conversation_id: str) -> bool:
        """Delete a conversation and all its messages"""
        try:
            from models import db, LLMConversation

            # Delete all messages in the conversation
            db.session.execute(
                db.delete(LLMConversation).filter_by(conversation_id=conversation_id)
            )
            db.session.commit()

            logger.info(f"Deleted conversation {conversation_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting conversation: {e}")
            db.session.rollback()
            return False
