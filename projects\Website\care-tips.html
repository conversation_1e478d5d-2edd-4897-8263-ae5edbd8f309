```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="A blog dedicated to all things <PERSON><PERSON><PERSON><PERSON>, including care tips, training advice, fun stories, and more.">
    <title>Care Tips - <PERSON> Snippets</title>
    <style>
        :root {
            --primary: #4a6fa5;
            --secondary: #f4f4f4;
            --accent: #e67e22;
            --text: #333;
            --light: #fff;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text);
            background-color: #f9f9f9;
        }
        
        header {
            background-color: var(--primary);
            color: var(--light);
            padding: 1rem;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-bottom: 10px;
        }
        
        .logo {
            width: 50px;
            height: 50px;
            background-color: var(--light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            font-weight: bold;
        }
        
        nav ul {
            display: flex;
            justify-content: center;
            list-style: none;
            flex-wrap: wrap;
            gap: 20px;
            padding: 10px 0;
        }
        
        nav a {
            color: var(--light);
            text-decoration: none;
            font-weight: 600;
            padding: 5px 10px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        nav a:hover, nav a:focus {
            background-color: rgba(255,255,255,0.2);
            outline: none;
        }
        
        .current-page {
            border-bottom: 3px solid var(--accent);
        }
        
        main {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 20px;
        }
        
        .page-title {
            text-align: center;
            margin-bottom: 2rem;
            color: var(--primary);
        }
        
        .content-section {
            background: var(--light);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .care-tip {
            border-left: 4px solid var(--accent);
            padding-left: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .care-tip h3 {
            color: var(--accent);
            margin-bottom: 0.5rem;
        }
        
        .tip-image {
            width: 100%;
            max-height: 300px;
            object-fit: cover;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .grooming-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 1.5rem 0;
        }
        
        .grooming-gallery img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            transition: transform 0.3s ease;
        }
        
        .grooming-gallery img:hover {
            transform: scale(1.03);
        }
        
        footer {
            background-color: var(--primary);
            color: var(--light);
            text-align: center;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .footer-nav {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 1rem;
        }
        
        .footer-nav a {
            color: var(--secondary);
            text-decoration: none;
        }
        
        .footer-nav a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .logo-container {
                flex-direction: column;
            }
            
            nav ul {
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }
            
            .content-section {
                padding: 1.5rem;
            }
        }
        
        @media (max-width: 480px) {
            .page-title {
                font-size: 1.5rem;
            }
            
            .care-tip {
                padding-left: 1rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="logo-container">
            <div class="logo" aria-label="Schnauzer Snippets Logo">SS</div>
            <h1>Schnauzer Snippets</h1>
        </div>
        <nav aria-label="Main navigation">
            <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="care-tips.html" class="current-page" aria-current="page">Care Tips</a></li>
                <li><a href="training.html">Training</a></li>
                <li><a href="stories.html">Stories</a></li>
                <li><a href="gallery.html">Gallery</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <h2 class="page-title">Essential Schnauzer Care Tips</h2>
        
        <section class="content-section" aria-labelledby="grooming">
            <div class="care-tip">
                <h3 id="grooming">Grooming & Coat Care</h3>
                <p>Schnauzers require regular grooming to maintain their distinctive appearance and prevent matting. Brush their wiry coat at least twice weekly using a slicker brush. Professional grooming is recommended every 6-8 weeks to trim the coat and maintain breed-specific features like their beard and eyebrows.</p>
                <div class="grooming-gallery" aria-label="Grooming examples">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='150' viewBox='0 0 200 150'%3E%3Crect width='200' height='150' fill='%234a6fa5'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='16' fill='%23fff' text-anchor='middle' dominant-baseline='middle'%3EGrooming Tools%3C/text%3E%3C/svg%3E" alt="Schnauzer grooming tools">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='150' viewBox='0 0 200 150'%3E%3Crect width='200' height='150' fill='%23e67e22'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='