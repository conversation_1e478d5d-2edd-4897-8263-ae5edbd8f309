#!/usr/bin/env python3
"""
Test script for Phase 1: Workflow Detection & Planning
Tests the new workflow analysis capabilities
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.unified_agent import WorkflowAnalyzer

def test_workflow_detection():
    """Test workflow detection with various prompts"""
    analyzer = WorkflowAnalyzer()
    
    test_cases = [
        # Should trigger workflow
        ("Create me a website for Schnauzers", True),
        ("Build a complete website with multiple pages", True),
        ("Make a responsive website with CSS and JavaScript", True),
        ("Create an app for dog lovers", True),
        ("Develop a project with multiple files", True),
        
        # Should NOT trigger workflow
        ("Show me the contents of index.html", False),
        ("What files do I have?", False),
        ("Help me understand CSS", False),
        ("Read the home.html file", False),
        ("List my project files", False),
    ]
    
    print("🧪 **Testing Phase 1: Workflow Detection**\n")
    
    for prompt, expected_workflow in test_cases:
        result = analyzer.analyze_request(prompt)
        status = "✅" if result.requires_workflow == expected_workflow else "❌"
        
        print(f"{status} **Prompt:** {prompt}")
        print(f"   **Workflow Required:** {result.requires_workflow} (expected: {expected_workflow})")
        print(f"   **Complexity Score:** {result.complexity_score:.2f}")
        
        if result.workflow_plan:
            print(f"   **Plan Title:** {result.workflow_plan.title}")
            print(f"   **Steps:** {result.workflow_plan.total_steps}")
        
        print()

def test_workflow_plan_generation():
    """Test workflow plan generation for complex requests"""
    analyzer = WorkflowAnalyzer()
    
    print("🎯 **Testing Workflow Plan Generation**\n")
    
    test_prompt = "Create me a website for Schnauzers with multiple pages"
    result = analyzer.analyze_request(test_prompt)
    
    if result.workflow_plan:
        plan = result.workflow_plan
        print(f"**Title:** {plan.title}")
        print(f"**Description:** {plan.description}")
        print(f"**Total Steps:** {plan.total_steps}")
        print(f"**Estimated Duration:** {plan.estimated_duration}")
        print("\n**Steps:**")
        
        for step in plan.steps:
            print(f"{step.step_number}. {step.description}")
            if step.file_path:
                print(f"   → File: {step.file_path}")
            if step.estimated_time:
                print(f"   → Time: {step.estimated_time}")
            print()
    else:
        print("❌ No workflow plan generated")

if __name__ == "__main__":
    test_workflow_detection()
    print("=" * 60)
    test_workflow_plan_generation()
    print("\n🎉 **Phase 1 Testing Complete!**")
