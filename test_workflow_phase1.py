#!/usr/bin/env python3
"""
Test script for Phase 1.2: Enhanced Workflow Detection & Planning with Toggle
Tests the enhanced workflow analysis capabilities and toggle feature
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.unified_agent import WorkflowAnaly<PERSON>

def test_workflow_detection():
    """Test workflow detection with various prompts"""
    analyzer = WorkflowAnalyzer()
    
    test_cases = [
        # Should trigger workflow
        ("Create me a website for Schnauzers", True),
        ("Build a complete website with multiple pages", True),
        ("Make a responsive website with CSS and JavaScript", True),
        ("Create an app for dog lovers", True),
        ("Develop a project with multiple files", True),
        
        # Should NOT trigger workflow
        ("Show me the contents of index.html", False),
        ("What files do I have?", False),
        ("Help me understand CSS", False),
        ("Read the home.html file", False),
        ("List my project files", False),
    ]
    
    print("🧪 **Testing Phase 1: Workflow Detection**\n")
    
    for prompt, expected_workflow in test_cases:
        result = analyzer.analyze_request(prompt)
        status = "✅" if result.requires_workflow == expected_workflow else "❌"
        
        print(f"{status} **Prompt:** {prompt}")
        print(f"   **Workflow Required:** {result.requires_workflow} (expected: {expected_workflow})")
        print(f"   **Complexity Score:** {result.complexity_score:.2f}")
        
        if result.workflow_plan:
            print(f"   **Plan Title:** {result.workflow_plan.title}")
            print(f"   **Steps:** {result.workflow_plan.total_steps}")
        
        print()

def test_workflow_plan_generation():
    """Test workflow plan generation for complex requests"""
    analyzer = WorkflowAnalyzer()
    
    print("🎯 **Testing Workflow Plan Generation**\n")
    
    test_prompt = "Create me a website for Schnauzers with multiple pages"
    result = analyzer.analyze_request(test_prompt)
    
    if result.workflow_plan:
        plan = result.workflow_plan
        print(f"**Title:** {plan.title}")
        print(f"**Description:** {plan.description}")
        print(f"**Total Steps:** {plan.total_steps}")
        print(f"**Estimated Duration:** {plan.estimated_duration}")
        print("\n**Steps:**")
        
        for step in plan.steps:
            print(f"{step.step_number}. {step.description}")
            if step.file_path:
                print(f"   → File: {step.file_path}")
            if step.estimated_time:
                print(f"   → Time: {step.estimated_time}")
            print()
    else:
        print("❌ No workflow plan generated")

def test_enhanced_features():
    """Test Phase 1.2 enhanced features"""
    analyzer = WorkflowAnalyzer()

    print("🚀 **Testing Phase 1.2: Enhanced Features**\n")

    # Test enhanced project analysis
    test_prompt = "Create a responsive website for Schnauzers with navigation, gallery, and contact form"
    result = analyzer.analyze_request_with_toggle(test_prompt)

    if result.workflow_plan:
        plan = result.workflow_plan
        print(f"**Enhanced Analysis Results:**")
        print(f"Title: {plan.title}")
        print(f"Complexity: {'Advanced' if 'advanced' in plan.description else 'Intermediate' if 'intermediate' in plan.description else 'Basic'}")
        print(f"Steps: {plan.total_steps}")
        print(f"Duration: {plan.estimated_duration}")
        print()

def test_toggle_feature():
    """Test the manual planning toggle feature"""
    analyzer = WorkflowAnalyzer()

    print("🎯 **Testing Toggle Feature**\n")

    toggle_test_cases = [
        # Should trigger planning mode
        ("Plan this: Create a simple HTML page", True),
        ("Create a plan for a dog website", True),
        ("Show me a step-by-step plan for building an app", True),
        ("Make a plan to create a contact form", True),

        # Normal requests (should use auto-detection)
        ("Create a website for cats", None),  # Should auto-detect
        ("Show me index.html", None),  # Should not trigger workflow
    ]

    for prompt, expected_planning in toggle_test_cases:
        result = analyzer.analyze_request_with_toggle(prompt)

        if expected_planning is True:
            status = "✅" if result.requires_workflow else "❌"
            print(f"{status} **Toggle Test:** {prompt}")
            print(f"   **Planning Triggered:** {result.requires_workflow}")
            print(f"   **Complexity Score:** {result.complexity_score:.2f}")
        else:
            print(f"🔄 **Auto-Detection:** {prompt}")
            print(f"   **Workflow Required:** {result.requires_workflow}")
            print(f"   **Complexity Score:** {result.complexity_score:.2f}")

        if result.workflow_plan:
            print(f"   **Plan Generated:** {result.workflow_plan.title}")
        print()

def test_enhanced_workflow_types():
    """Test different types of enhanced workflows"""
    analyzer = WorkflowAnalyzer()

    print("🎨 **Testing Enhanced Workflow Types**\n")

    workflow_types = [
        ("Create a game for kids", "Game Development"),
        ("Build an API for user management", "Backend/API"),
        ("Make a responsive portfolio website", "Website"),
        ("Develop a Python application for data analysis", "Application"),
    ]

    for prompt, expected_type in workflow_types:
        result = analyzer.analyze_request_with_toggle(prompt)

        if result.workflow_plan:
            plan = result.workflow_plan
            print(f"**Prompt:** {prompt}")
            print(f"**Type Detected:** {expected_type}")
            print(f"**Plan Title:** {plan.title}")
            print(f"**Steps:** {plan.total_steps}")
            print(f"**First Step:** {plan.steps[0].description if plan.steps else 'None'}")
            print()

if __name__ == "__main__":
    print("🧪 **Phase 1.2: Enhanced Workflow Testing**\n")

    test_workflow_detection()
    print("=" * 60)
    test_workflow_plan_generation()
    print("=" * 60)
    test_enhanced_features()
    print("=" * 60)
    test_toggle_feature()
    print("=" * 60)
    test_enhanced_workflow_types()

    print("\n🎉 **Phase 1.2 Testing Complete!**")
    print("✅ Enhanced workflow detection")
    print("✅ Toggle feature for manual planning")
    print("✅ Multiple workflow types supported")
    print("✅ Advanced project analysis")
    print("✅ Enhanced plan formatting ready")
