"""
Flask + SQLite + OpenRouter AI App
Main application entry point with async support
"""
import os
import hashlib
import uuid
import time
from flask import Flask, request, jsonify, render_template, send_from_directory, Response
from werkzeug.utils import secure_filename
import logging

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass  # python-dotenv not installed

# Simple in-memory cache for AI responses (Week 3 Performance Improvement)
response_cache = {}

def get_cache_key(prompt: str, context: dict = None) -> str:
    """Generate a cache key for AI responses"""
    cache_data = f"{prompt}:{str(context or {})}"
    return hashlib.md5(cache_data.encode()).hexdigest()

def get_cached_response(cache_key: str) -> str:
    """Get cached AI response if available and not expired"""
    if cache_key in response_cache:
        cached_time, response = response_cache[cache_key]
        # Cache for 1 hour (3600 seconds)
        if time.time() - cached_time < 3600:
            logger.info(f"Cache hit for key: {cache_key[:8]}...")
            return response
        else:
            # Remove expired cache entry
            del response_cache[cache_key]
    return None

def cache_response(cache_key: str, response: str):
    """Cache AI response with timestamp"""
    response_cache[cache_key] = (time.time(), response)

    # Keep cache size reasonable (max 100 entries)
    if len(response_cache) > 100:
        # Remove oldest entries
        oldest_key = min(response_cache.keys(),
                        key=lambda k: response_cache[k][0])
        del response_cache[oldest_key]
        logger.info("Cache cleanup: removed oldest entry")

def handle_api_error(e: Exception, endpoint: str = "unknown") -> tuple:
    """Consistent error handling with better logging (Week 3 Performance Improvement)"""
    import traceback
    from datetime import datetime

    error_id = hashlib.md5(f"{str(e)}{time.time()}".encode()).hexdigest()[:8]

    logger.error(f"API Error [{error_id}] in {endpoint}: {str(e)}")
    logger.error(f"Traceback [{error_id}]: {traceback.format_exc()}")

    return jsonify({
        'error': str(e),
        'success': False,
        'error_id': error_id,
        'timestamp': datetime.utcnow().isoformat(),
        'endpoint': endpoint
    }), 500

# Import our modules
from models import db, Job, LLMConversation
from tools.file_tools import FileTools, FileReadRequest
from tools.llm_tools import LLMTools, LLMRequest
from tools.diff_tools import DiffTools, DiffRequest
from config import config
# Week 4 VITALLY IMPORTANT: Import context management
from tools.context_manager import context_manager, calculate_token_usage, format_token_display
from tools.activity_tracker import activity_tracker, ActivityType

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global scheduler instance (will be initialized in create_app)
scheduler = None

# File upload configuration
ALLOWED_EXTENSIONS = {
    'txt', 'py', 'js', 'html', 'css', 'json', 'xml', 'yaml', 'yml', 'md', 'rst',
    'pdf', 'doc', 'docx', 'png', 'jpg', 'jpeg', 'gif', 'svg', 'csv', 'tsv'
}

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def calculate_file_hash(file_path):
    """Calculate SHA-256 hash of file"""
    hash_sha256 = hashlib.sha256()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_sha256.update(chunk)
    return hash_sha256.hexdigest()

def read_file_content(file_path):
    """Read file content as text (for text files)"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except UnicodeDecodeError:
        # For binary files, return a placeholder
        return f"[Binary file: {os.path.basename(file_path)}]"

def create_app(test_config=None):
    """Application factory"""
    app = Flask(__name__, instance_relative_config=True)

    # Load configuration
    config_name = os.environ.get('FLASK_ENV', 'development')
    app.config.from_object(config[config_name])

    if test_config is not None:
        app.config.from_mapping(test_config)

    # File upload configuration
    app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
    app.config['UPLOAD_FOLDER'] = os.path.join(app.instance_path, 'uploads')

    # Ensure instance and upload folders exist
    try:
        os.makedirs(app.instance_path, exist_ok=True)
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    except OSError:
        pass
    
    # Initialize extensions
    db.init_app(app)
    
    # Register routes
    register_routes(app)
    
    return app

def register_routes(app: Flask):
    """Register all application routes"""
    
    @app.route('/')
    def index():
        """Main page"""
        return render_template('index.html')

    @app.route('/files')
    def files():
        """Files management page"""
        return render_template('files.html')

    @app.route('/activity-stream')
    def activity_stream():
        """Server-Sent Events stream for AI activity updates"""
        import json
        import time

        def generate_events():
            """Generate Server-Sent Events"""
            try:
                # Send connection confirmation
                yield f"data: {json.dumps({'type': 'connected', 'message': 'Activity stream connected'})}\n\n"

                last_activity = None
                heartbeat_counter = 0

                while True:
                    try:
                        # Check current activity
                        current_activity = activity_tracker.get_current_activity()

                        if current_activity and current_activity != last_activity:
                            # Send activity update
                            activity_data = {
                                'activity_type': current_activity.activity_type.value,
                                'description': current_activity.description,
                                'progress': current_activity.progress,
                                'timestamp': current_activity.timestamp.isoformat(),
                                'metadata': current_activity.metadata or {}
                            }
                            yield f"event: ai_activity\ndata: {json.dumps(activity_data)}\n\n"
                            last_activity = current_activity
                            logger.info(f"📡 Sent activity event: {current_activity.activity_type.value}")

                        elif not current_activity and last_activity:
                            # Activity ended
                            yield f"event: ai_activity\ndata: {json.dumps({'type': 'activity_ended'})}\n\n"
                            last_activity = None
                            logger.info("📡 Sent activity ended event")

                        # Send heartbeat every 30 seconds
                        heartbeat_counter += 1
                        if heartbeat_counter >= 30:
                            yield f"data: {json.dumps({'type': 'heartbeat', 'timestamp': time.time()})}\n\n"
                            heartbeat_counter = 0

                        time.sleep(1)  # Check for updates every second

                    except Exception as e:
                        logger.error(f"❌ Error in activity stream loop: {e}")
                        yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"
                        break

            except Exception as e:
                logger.error(f"❌ Error in event generator: {e}")
                yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"

        return Response(
            generate_events(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'X-Accel-Buffering': 'no'  # Disable nginx buffering
            }
        )

    @app.route('/api/activity/current')
    def get_current_activity():
        """Get current activity status (polling endpoint)"""
        try:
            current_activity = activity_tracker.get_current_activity()

            if current_activity:
                return jsonify({
                    'active': True,
                    'activity_type': current_activity.activity_type.value,
                    'description': current_activity.description,
                    'progress': current_activity.progress,
                    'timestamp': current_activity.timestamp.isoformat(),
                    'metadata': current_activity.metadata or {}
                })
            else:
                return jsonify({
                    'active': False,
                    'activity_type': None,
                    'description': None,
                    'progress': None,
                    'timestamp': None,
                    'metadata': {}
                })

        except Exception as e:
            logger.error(f"❌ Error getting current activity: {e}")
            return jsonify({
                'error': str(e),
                'active': False
            }), 500
    
    @app.route('/api/tools/read_file', methods=['POST'])
    def read_file():
        """Read file content endpoint"""
        import asyncio
        try:
            data = FileReadRequest.model_validate(request.get_json())
            response = asyncio.run(FileTools.read_file(data))
            return jsonify(response.model_dump())

        except Exception as e:
            logger.error(f"Error reading file: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/api/tools/write_file', methods=['POST'])
    def write_file():
        """Write file content endpoint"""
        import asyncio
        try:
            from tools.file_tools import FileWriteRequest
            data = FileWriteRequest.model_validate(request.get_json())
            response = asyncio.run(FileTools.write_file(data))
            return jsonify(response.model_dump())

        except Exception as e:
            logger.error(f"Error writing file: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/tools/edit_diff', methods=['POST'])
    def edit_diff():
        """Generate diff between two text strings"""
        import asyncio
        try:
            data = DiffRequest.model_validate(request.get_json())
            response = asyncio.run(DiffTools.generate_diff(data))
            return jsonify(response.model_dump())

        except Exception as e:
            logger.error(f"Error generating diff: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/llm/query', methods=['POST'])
    def query_llm():
        """Query OpenRouter LLM endpoint"""
        import asyncio
        try:
            data = LLMRequest.model_validate(request.get_json())

            # Load conversation history if conversation_id is provided
            if data.conversation_id:
                history = asyncio.run(LLMTools.get_conversation_history(data.conversation_id))
                data.messages = history

            response = asyncio.run(LLMTools.query_llm(data))
            return jsonify(response.model_dump())

        except Exception as e:
            logger.error(f"Error querying LLM: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/api/llm/stream-reasoning', methods=['POST'])
    def stream_llm_with_reasoning():
        """Stream LLM response with real-time reasoning content"""
        import asyncio
        import json

        def generate_stream():
            try:
                data = LLMRequest.model_validate(request.get_json())

                # Load conversation history if conversation_id is provided
                if data.conversation_id:
                    history = asyncio.run(LLMTools.get_conversation_history(data.conversation_id))
                    data.messages = history

                # Track activity for real-time updates
                activity_tracker.start_activity(
                    activity_type=ActivityType.THINKING,
                    description="Analyzing your request...",
                    progress=0.0
                )

                async def stream_generator():
                    try:
                        async for chunk in LLMTools.stream_llm_with_reasoning(data):
                            if chunk['type'] == 'reasoning':
                                # Update activity with reasoning content
                                reasoning_snippet = chunk['content'][-100:] if len(chunk['content']) > 100 else chunk['content']
                                activity_tracker.update_activity(
                                    activity_type=ActivityType.THINKING,
                                    description=f"Reasoning: {reasoning_snippet.strip()}",
                                    progress=chunk['progress']
                                )

                                # Yield reasoning update
                                yield f"data: {json.dumps({'type': 'reasoning', 'content': chunk['content'], 'progress': chunk['progress']})}\n\n"

                            elif chunk['type'] == 'content':
                                # Switch to writing activity
                                activity_tracker.update_activity(
                                    activity_type=ActivityType.WRITING,
                                    description="Crafting response...",
                                    progress=chunk['progress']
                                )

                                # Yield content update
                                yield f"data: {json.dumps({'type': 'content', 'content': chunk['content'], 'progress': chunk['progress']})}\n\n"

                        # Complete activity
                        activity_tracker.end_activity()
                        yield f"data: {json.dumps({'type': 'complete'})}\n\n"

                    except Exception as e:
                        activity_tracker.end_activity()
                        logger.error(f"Error in stream generator: {e}")
                        yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"

                # Run the async generator
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    async_gen = stream_generator()
                    while True:
                        try:
                            chunk = loop.run_until_complete(async_gen.__anext__())
                            yield chunk
                        except StopAsyncIteration:
                            break
                finally:
                    loop.close()

            except Exception as e:
                activity_tracker.end_activity()
                logger.error(f"Error in stream_llm_with_reasoning: {e}")
                yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"

        return Response(
            generate_stream(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'X-Accel-Buffering': 'no'
            }
        )

    @app.route('/api/llm/conversations', methods=['GET'])
    def get_conversations():
        """Get list of conversations"""
        import asyncio
        try:
            limit = request.args.get('limit', 50, type=int)
            response = asyncio.run(LLMTools.get_conversations_list(limit))
            return jsonify(response.model_dump())

        except Exception as e:
            logger.error(f"Error getting conversations: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/api/llm/conversations/<conversation_id>', methods=['GET'])
    def get_conversation_history(conversation_id: str):
        """Get conversation history"""
        import asyncio
        try:
            messages = asyncio.run(LLMTools.get_conversation_history(conversation_id))
            return jsonify([msg.model_dump() for msg in messages])

        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/api/llm/conversations/<conversation_id>', methods=['DELETE'])
    def delete_conversation(conversation_id: str):
        """Delete a conversation"""
        import asyncio
        try:
            success = asyncio.run(LLMTools.delete_conversation(conversation_id))
            if success:
                return jsonify({'message': 'Conversation deleted successfully'})
            else:
                return jsonify({'error': 'Failed to delete conversation'}), 500

        except Exception as e:
            logger.error(f"Error deleting conversation: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/api/files/upload', methods=['POST'])
    def upload_file():
        """Upload file endpoint"""
        try:
            # Check if file is in request
            if 'file' not in request.files:
                return jsonify({'error': 'No file provided'}), 400

            file = request.files['file']

            # Check if file was selected
            if file.filename == '':
                return jsonify({'error': 'No file selected'}), 400

            # Validate file
            if not allowed_file(file.filename):
                return jsonify({'error': 'File type not allowed'}), 400

            # Generate unique filename
            filename = secure_filename(file.filename)
            file_id = str(uuid.uuid4())
            file_extension = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
            unique_filename = f"{file_id}.{file_extension}" if file_extension else file_id

            # Ensure upload directory exists
            os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

            # Save file
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
            file.save(file_path)

            # Calculate file hash
            file_hash = calculate_file_hash(file_path)

            # Save to database
            from models import db, File
            file_record = File(
                file_path=unique_filename,
                content=read_file_content(file_path),
                content_hash=file_hash,
                file_size=os.path.getsize(file_path),
                mime_type=file.content_type,
                encoding='utf-8'
            )

            db.session.add(file_record)
            db.session.commit()

            return jsonify({
                'id': file_record.id,
                'filename': filename,
                'size': file_record.file_size,
                'mime_type': file_record.mime_type,
                'hash': file_hash
            })

        except Exception as e:
            logger.error(f"Error uploading file: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/api/files/<int:file_id>', methods=['GET'])
    def get_file(file_id: int):
        """Get file metadata"""
        try:
            from models import db, File
            file_record = db.get_or_404(File, file_id)
            return jsonify(file_record.to_dict())

        except Exception as e:
            logger.error(f"Error getting file: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/api/files/<int:file_id>/download', methods=['GET'])
    def download_file(file_id: int):
        """Download file"""
        try:
            from models import db, File
            file_record = db.get_or_404(File, file_id)

            file_path = os.path.join(app.config['UPLOAD_FOLDER'], file_record.file_path)

            if not os.path.exists(file_path):
                return jsonify({'error': 'File not found on disk'}), 404

            # Update access time
            file_record.update_access_time()
            db.session.commit()

            return send_from_directory(
                app.config['UPLOAD_FOLDER'],
                file_record.file_path,
                as_attachment=True,
                download_name=file_record.file_path
            )

        except Exception as e:
            logger.error(f"Error downloading file: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/api/files', methods=['GET'])
    def list_files():
        """List uploaded files"""
        try:
            from models import db, File
            files = db.session.execute(
                db.select(File).order_by(File.created_at.desc()).limit(100)
            ).scalars()

            return jsonify([file.to_dict() for file in files])

        except Exception as e:
            logger.error(f"Error listing files: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/api/unified-ai/process', methods=['POST'])
    def unified_ai_process():
        """Process any AI request using unified intelligent agent with caching"""
        import asyncio
        try:
            data = request.get_json()
            prompt = data.get('prompt', '')
            conversation_id = data.get('conversation_id', None)
            context = data.get('context', {})

            # SECURITY: Log request details for monitoring
            logger.info(f"🔒 SECURITY: AI request from IP: {request.remote_addr}")
            logger.info(f"🔒 SECURITY: Prompt length: {len(prompt)} chars")
            logger.info(f"🔒 SECURITY: Conversation ID: {conversation_id}")

            # SECURITY: Check for suspicious patterns
            suspicious_patterns = ['@/', '@\\', '@C:', '@D:', 'venv', 'site-packages', 'botocore', 'model.py']
            for pattern in suspicious_patterns:
                if pattern in prompt:
                    logger.warning(f"🚨 SECURITY ALERT: Suspicious pattern '{pattern}' detected in prompt")

            if not prompt:
                return jsonify({'error': 'Prompt is required'}), 400

            # Check cache first (Week 3 Performance Improvement)
            cache_key = get_cache_key(prompt, context)
            cached_response = get_cached_response(cache_key)

            if cached_response:
                return jsonify({
                    'response': cached_response,
                    'success': True,
                    'conversation_id': conversation_id,
                    'cached': True  # Indicate this was from cache
                })

            from tools.unified_agent import get_unified_agent
            agent = get_unified_agent()

            # Process request with intelligent tool routing
            response = asyncio.run(agent.process_request(
                prompt=prompt,
                conversation_id=conversation_id,
                user_context=context
            ))

            # Week 4 VITALLY IMPORTANT: Save conversation to database with context
            try:
                # Ensure we have a valid conversation_id
                if not conversation_id:
                    logger.error("❌ No conversation_id available for database save - skipping")
                    raise ValueError("No conversation_id provided")

                # Get context stats for this conversation
                context_stats = context_manager.get_context_stats(conversation_id)
                message_history = context_manager.get_message_history(conversation_id)

                # Create conversation record
                conversation = LLMConversation(
                    conversation_id=conversation_id,
                    model='deepseek/deepseek-r1-0528:free',
                    prompt=prompt,
                    response=response,
                    message_history=context_manager.serialize_messages(message_history),
                    request_tokens=context_stats.get('total_tokens', 0),  # Will be updated with actual usage
                    response_tokens=0,  # Will be updated with actual usage
                    total_tokens=context_stats.get('total_tokens', 0),
                    cumulative_tokens=context_stats.get('total_tokens', 0),
                    context_length=context_stats.get('message_count', 0)
                )

                db.session.add(conversation)
                db.session.commit()
                logger.info(f"💾 Saved conversation to database: {conversation_id}")

            except Exception as e:
                logger.error(f"❌ Error saving conversation to database: {e}")
                # Don't fail the request if database save fails

            # Cache the response (Week 3 Performance Improvement)
            cache_response(cache_key, response)

            # Week 4 VITALLY IMPORTANT: Include context stats in response
            context_stats = context_manager.get_context_stats(conversation_id)

            return jsonify({
                'response': response,
                'success': True,
                'conversation_id': conversation_id,
                'cached': False,  # Indicate this was fresh
                # Week 4 Context Management: Include token usage info
                'context_stats': {
                    'total_tokens': context_stats.get('total_tokens', 0),
                    'message_count': context_stats.get('message_count', 0),
                    'token_display': format_token_display(context_stats),
                    'context_health': context_stats.get('context_health', 'unknown')
                }
            })

        except Exception as e:
            return handle_api_error(e, "unified-ai-process")

    @app.route('/api/unified-ai/context-stats', methods=['GET'])
    def get_context_stats():
        """Get context statistics for all conversations (Week 4 Context Management)"""
        try:
            conversation_id = request.args.get('conversation_id')

            if conversation_id:
                # Get stats for specific conversation
                stats = context_manager.get_context_stats(conversation_id)
                return jsonify({
                    'success': True,
                    'conversation_stats': stats,
                    'token_display': format_token_display(stats)
                })
            else:
                # Get stats for all conversations
                all_conversations = context_manager.get_all_conversations()
                return jsonify({
                    'success': True,
                    'all_conversations': all_conversations,
                    'total_conversations': len(all_conversations)
                })

        except Exception as e:
            return handle_api_error(e, "context-stats")

    @app.route('/api/unified-ai/context-compact', methods=['POST'])
    def compact_context():
        """Intelligent context compaction for conversation (Week 5 REVOLUTIONARY)"""
        try:
            data = request.get_json()
            conversation_id = data.get('conversation_id')

            if not conversation_id:
                return jsonify({'error': 'Conversation ID required'}), 400

            # Perform intelligent compaction
            result = context_manager.compact_context(conversation_id)

            if result['success']:
                return jsonify({
                    'success': True,
                    'message': f"Context compacted successfully! Reduced from {result['old_tokens']:,} to {result['new_tokens']:,} tokens ({result['reduction_percentage']:.1f}% reduction)",
                    'stats': {
                        'old_tokens': result['old_tokens'],
                        'new_tokens': result['new_tokens'],
                        'reduction_percentage': result['reduction_percentage'],
                        'old_messages': result['old_messages'],
                        'new_messages': result['new_messages']
                    }
                })
            else:
                return jsonify({'error': result['error']}), 400

        except Exception as e:
            return handle_api_error(e, "context-compact")

    @app.route('/api/ai-coder/file-operation', methods=['POST'])
    def ai_coder_file_operation():
        """Execute file operation using AI coding agent"""
        import asyncio
        try:
            data = request.get_json()
            operation = data.get('operation')
            file_path = data.get('file_path')
            content = data.get('content', '')

            if not operation or not file_path:
                return jsonify({'error': 'operation and file_path are required'}), 400

            from tools.unified_agent import get_unified_agent
            ai_agent = get_unified_agent()

            # Handle operations directly for better reliability
            if operation == 'read':
                # Direct file reading without AI agent
                try:
                    from tools.project_manager import project_manager
                    project_folder = project_manager.get_project_folder()
                    full_path = project_folder / file_path

                    # Security check
                    try:
                        full_path.relative_to(project_folder)
                    except ValueError:
                        return jsonify({'error': 'Access denied: path outside project'}), 403

                    if not full_path.exists():
                        return jsonify({'error': f'File "{file_path}" not found'}), 404

                    if not full_path.is_file():
                        return jsonify({'error': f'"{file_path}" is not a file'}), 400

                    # Read file content
                    actual_content = full_path.read_text(encoding='utf-8')
                    file_size = len(actual_content.encode('utf-8'))

                    return jsonify({
                        'success': True,
                        'operation': operation,
                        'file_path': file_path,
                        'message': f'Successfully read file "{file_path}"',
                        'content': actual_content,
                        'file_size': file_size
                    })

                except Exception as e:
                    return jsonify({
                        'success': False,
                        'operation': operation,
                        'file_path': file_path,
                        'message': f'Error reading file: {str(e)}',
                        'content': None,
                        'file_size': None
                    }), 500

            elif operation == 'write':
                # Direct file writing without AI agent
                try:
                    from tools.project_manager import project_manager
                    project_folder = project_manager.get_project_folder()
                    full_path = project_folder / file_path

                    # Security check
                    try:
                        full_path.relative_to(project_folder)
                    except ValueError:
                        return jsonify({'error': 'Access denied: path outside project'}), 403

                    if not full_path.exists():
                        return jsonify({'error': f'File "{file_path}" not found'}), 404

                    if not full_path.is_file():
                        return jsonify({'error': f'"{file_path}" is not a file'}), 400

                    # Write file content
                    full_path.write_text(content, encoding='utf-8')
                    file_size = len(content.encode('utf-8'))

                    return jsonify({
                        'success': True,
                        'operation': operation,
                        'file_path': file_path,
                        'message': f'Successfully saved file "{file_path}"',
                        'content': content,
                        'file_size': file_size
                    })

                except Exception as e:
                    return jsonify({
                        'success': False,
                        'operation': operation,
                        'file_path': file_path,
                        'message': f'Error saving file: {str(e)}',
                        'content': None,
                        'file_size': None
                    }), 500

            # For other operations, use AI agent with caching
            if operation == 'create':
                prompt = f"Create a file at '{file_path}' with the following content:\n\n{content}"
            elif operation == 'list':
                prompt = f"List all files in directory '{file_path}'"
            elif operation == 'delete':
                prompt = f"Delete the file '{file_path}'"
            else:
                return jsonify({'error': f'Unknown operation: {operation}'}), 400

            # Check cache for non-destructive operations (Week 3 Performance Improvement)
            cache_key = None
            if operation in ['list']:  # Only cache read-only operations
                cache_key = get_cache_key(prompt, {'operation': operation, 'file_path': file_path})
                cached_response = get_cached_response(cache_key)
                if cached_response:
                    return jsonify({
                        'success': True,
                        'operation': operation,
                        'file_path': file_path,
                        'message': cached_response,
                        'cached': True
                    })

            response = asyncio.run(ai_agent.process_request(
                prompt=prompt,
                conversation_id=None,
                user_context={}
            ))

            # Cache the response for read-only operations
            if cache_key:
                cache_response(cache_key, response)

            # Parse response to determine success
            success = not response.startswith('Error:')

            return jsonify({
                'success': success,
                'operation': operation,
                'file_path': file_path,
                'message': response,
                'content': content if operation in ['create'] else None,
                'file_size': len(content.encode('utf-8')) if content else None
            })

        except Exception as e:
            return handle_api_error(e, "ai-coder-file-operation")

    @app.route('/api/ai-coder/list-workspace', methods=['GET'])
    def ai_coder_list_workspace():
        """List project workspace files"""
        import json
        try:
            directory = request.args.get('directory', '.')

            # Get current project folder
            from tools.project_manager import project_manager
            project_folder = project_manager.get_project_folder()

            # Convert to Path object and resolve within project
            if directory == '.':
                dir_path = project_folder
            else:
                dir_path = (project_folder / directory).resolve()

            # Security check: ensure we're not going outside the project folder
            try:
                dir_path.relative_to(project_folder)
            except ValueError:
                return jsonify({'error': 'Access denied: path outside project'}), 403

            if not dir_path.exists():
                return jsonify({'error': 'Directory not found'}), 404

            if not dir_path.is_dir():
                return jsonify({'error': 'Path is not a directory'}), 400

            files = []
            for item in dir_path.iterdir():
                if item.name.startswith('.'):
                    continue

                file_info = {
                    'name': item.name,
                    'path': str(item.relative_to(project_folder)),
                    'is_file': item.is_file(),
                    'is_dir': item.is_dir(),
                    'size': item.stat().st_size if item.is_file() else None,
                    'extension': item.suffix[1:] if item.is_file() and item.suffix else None
                }
                files.append(file_info)

            # Sort: directories first, then files, both alphabetically
            files.sort(key=lambda x: (x['is_file'], x['name'].lower()))

            # Get current project info
            current_project = project_manager.get_current_project()

            return jsonify({
                'success': True,
                'operation': 'list',
                'file_path': directory,
                'content': json.dumps(files),
                'project': current_project.model_dump() if current_project else None,
                'message': f"📁 Listed {len(files)} items in project directory"
            })

        except Exception as e:
            logger.error(f"Error listing workspace: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/jobs', methods=['GET'])
    def get_jobs():
        """Get all jobs"""
        jobs = db.session.execute(db.select(Job).order_by(Job.created_at.desc())).scalars()
        return jsonify([{
            'id': job.id,
            'job_id': job.job_id,
            'status': job.status,
            'created_at': job.created_at.isoformat(),
            'completed_at': job.completed_at.isoformat() if job.completed_at else None
        } for job in jobs])
    
    @app.route('/api/jobs/<job_id>', methods=['GET'])
    def get_job(job_id: str):
        """Get specific job details"""
        job = db.session.execute(
            db.select(Job).filter_by(job_id=job_id)
        ).scalar_one_or_none()

        if not job:
            return jsonify({'error': 'Job not found'}), 404

        return jsonify({
            'id': job.id,
            'job_id': job.job_id,
            'status': job.status,
            'result': job.result,
            'error': job.error,
            'created_at': job.created_at.isoformat(),
            'completed_at': job.completed_at.isoformat() if job.completed_at else None
        })

    # Project Management Endpoints
    @app.route('/api/projects', methods=['GET'])
    def get_projects():
        """Get all projects"""
        try:
            from tools.project_manager import project_manager
            projects = project_manager.get_projects()
            current_project = project_manager.get_current_project()

            return jsonify({
                'projects': [p.model_dump() for p in projects],
                'current_project_id': current_project.id if current_project else None
            })
        except Exception as e:
            logger.error(f"Error getting projects: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/api/projects', methods=['POST'])
    def create_project():
        """Create a new project"""
        try:
            data = request.get_json()
            name = data.get('name', '').strip()
            description = data.get('description', '').strip()

            if not name:
                return jsonify({'error': 'Project name is required'}), 400

            from tools.project_manager import project_manager
            project = project_manager.create_project(name, description)

            return jsonify({
                'project': project.model_dump(),
                'message': f'Project "{name}" created successfully'
            })
        except Exception as e:
            logger.error(f"Error creating project: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/api/projects/<project_id>/switch', methods=['POST'])
    def switch_project(project_id: str):
        """Switch to a different project"""
        try:
            from tools.project_manager import project_manager
            success = project_manager.set_current_project(project_id)

            if not success:
                return jsonify({'error': 'Project not found'}), 404

            project = project_manager.get_project(project_id)
            return jsonify({
                'project': project.model_dump(),
                'message': f'Switched to project "{project.name}"'
            })
        except Exception as e:
            logger.error(f"Error switching project: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/api/projects/<project_id>', methods=['DELETE'])
    def delete_project(project_id: str):
        """Delete a project"""
        try:
            from tools.project_manager import project_manager
            project = project_manager.get_project(project_id)

            if not project:
                return jsonify({'error': 'Project not found'}), 404

            success = project_manager.delete_project(project_id)

            if success:
                return jsonify({'message': f'Project "{project.name}" deleted successfully'})
            else:
                return jsonify({'error': 'Failed to delete project'}), 500
        except Exception as e:
            logger.error(f"Error deleting project: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/api/projects/current', methods=['GET'])
    def get_current_project():
        """Get current project info"""
        try:
            from tools.project_manager import project_manager
            project = project_manager.get_current_project()

            if not project:
                return jsonify({'current_project': None})

            return jsonify({'current_project': project.model_dump()})
        except Exception as e:
            logger.error(f"Error getting current project: {e}")
            return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app = create_app()
    
    # Create tables
    with app.app_context():
        db.create_all()
        logger.info("Database tables created")
    
    # For development - use Flask's built-in server
    # For production, use ASGI server like uvicorn
    app.run(debug=True, host='0.0.0.0', port=5000)
