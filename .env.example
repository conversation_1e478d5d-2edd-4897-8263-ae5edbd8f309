# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Database Configuration
DATABASE_URL=sqlite:///app.db

# OpenRouter API Configuration
OPENROUTER_API_KEY=your-openrouter-api-key-here

# Logging Configuration
LOG_LEVEL=INFO

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_EXTENSIONS=.txt,.py,.js,.html,.css,.json,.md,.yml,.yaml

# Development Settings
DEBUG=True
