#!/usr/bin/env python3
"""
Test AI Coder endpoint directly
"""
import asyncio
import httpx

async def test_ai_coder():
    """Test AI Coder endpoint directly"""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print("🧪 Testing AI Coder endpoint directly...")
            
            response = await client.post(
                'http://localhost:5000/api/ai-coder/process',
                headers={'Content-Type': 'application/json'},
                json={
                    'prompt': 'Create a simple HTML file called test.html with a hello world message',
                    'context': None
                }
            )
            
            print(f"Status: {response.status_code}")
            print(f"Response: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"Success: {result.get('success', False)}")
                print(f"Response: {result.get('response', 'No response')}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_ai_coder())
