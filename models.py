"""
SQLAlchemy models for the Flask app
"""
from datetime import datetime, timezone
from typing import Optional
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy import Integer, String, Text, DateTime, JSON
# Boolean - commented out with unused models
from flask_sqlalchemy import SQLAlchemy

class Base(DeclarativeBase):
    pass

db = SQLAlchemy(model_class=Base)

# UNUSED MODEL - Commented out during cleanup (Week 1)
# class Tool(db.Model):
#     """Model for storing tool definitions"""
#     __tablename__ = 'tools'
#
#     id: Mapped[int] = mapped_column(Integer, primary_key=True)
#     name: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
#     description: Mapped[str] = mapped_column(Text, nullable=False)
#     endpoint: Mapped[str] = mapped_column(String(200), nullable=False)
#     method: Mapped[str] = mapped_column(String(10), default='POST')
#     parameters: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
#     created_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc))
#     updated_at: Mapped[datetime] = mapped_column(
#         DateTime,
#         default=lambda: datetime.now(timezone.utc),
#         onupdate=lambda: datetime.now(timezone.utc)
#     )
#     is_active: Mapped[bool] = mapped_column(Boolean, default=True)
#
#     def __repr__(self):
#         return f'<Tool {self.name}>'
#
#     def to_dict(self):
#         """Convert to dictionary for JSON serialization"""
#         return {
#             'id': self.id,
#             'name': self.name,
#             'description': self.description,
#             'endpoint': self.endpoint,
#             'method': self.method,
#             'parameters': self.parameters,
#             'created_at': self.created_at.isoformat(),
#             'updated_at': self.updated_at.isoformat(),
#             'is_active': self.is_active
#         }

class Job(db.Model):
    """Model for storing background job information"""
    __tablename__ = 'jobs'
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    job_id: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    job_type: Mapped[str] = mapped_column(String(50), nullable=False)
    status: Mapped[str] = mapped_column(String(50), default='pending')
    input_data: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    result: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    error: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    progress: Mapped[int] = mapped_column(Integer, default=0)  # 0-100
    created_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc))
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    def __repr__(self):
        return f'<Job {self.job_id} ({self.status})>'
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'job_id': self.job_id,
            'job_type': self.job_type,
            'status': self.status,
            'input_data': self.input_data,
            'result': self.result,
            'error': self.error,
            'progress': self.progress,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }
    
    def update_status(self, status: str, progress: int = None, error: str = None):
        """Update job status and related fields"""
        self.status = status
        if progress is not None:
            self.progress = progress
        if error:
            self.error = error
        
        if status == 'running' and not self.started_at:
            self.started_at = datetime.now(timezone.utc)
        elif status in ['completed', 'failed']:
            self.completed_at = datetime.now(timezone.utc)

class File(db.Model):
    """Model for storing file content and metadata"""
    __tablename__ = 'files'
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    file_path: Mapped[str] = mapped_column(String(500), unique=True, nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    content_hash: Mapped[str] = mapped_column(String(64), nullable=False)  # SHA-256 hash
    file_size: Mapped[int] = mapped_column(Integer, nullable=False)
    mime_type: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    encoding: Mapped[str] = mapped_column(String(20), default='utf-8')
    created_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc))
    last_modified: Mapped[datetime] = mapped_column(
        DateTime, 
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc)
    )
    last_accessed: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc))
    
    def __repr__(self):
        return f'<File {self.file_path}>'
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'file_path': self.file_path,
            'content_hash': self.content_hash,
            'file_size': self.file_size,
            'mime_type': self.mime_type,
            'encoding': self.encoding,
            'created_at': self.created_at.isoformat(),
            'last_modified': self.last_modified.isoformat(),
            'last_accessed': self.last_accessed.isoformat()
        }
    
    def update_access_time(self):
        """Update the last accessed timestamp"""
        self.last_accessed = datetime.now(timezone.utc)

class LLMConversation(db.Model):
    """Model for storing LLM conversation history with context management (Week 4 Enhancement)"""
    __tablename__ = 'llm_conversations'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    conversation_id: Mapped[str] = mapped_column(String(100), nullable=False)
    model: Mapped[str] = mapped_column(String(100), nullable=False)
    prompt: Mapped[str] = mapped_column(Text, nullable=False)
    response: Mapped[str] = mapped_column(Text, nullable=False)
    tokens_used: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    cost: Mapped[Optional[float]] = mapped_column(nullable=True)  # Cost in USD
    response_time: Mapped[Optional[float]] = mapped_column(nullable=True)  # Response time in seconds
    created_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Week 4 Context Management: Enhanced fields for proper context tracking
    message_history: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # JSON serialized Pydantic AI messages
    request_tokens: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0)
    response_tokens: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0)
    total_tokens: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0)
    cumulative_tokens: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0)  # Running total for conversation
    context_length: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0)  # Number of messages in context
    
    def __repr__(self):
        return f'<LLMConversation {self.conversation_id} ({self.model})>'
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization (Week 4 Enhanced)"""
        return {
            'id': self.id,
            'conversation_id': self.conversation_id,
            'model': self.model,
            'prompt': self.prompt,
            'response': self.response,
            'tokens_used': self.tokens_used,
            'cost': self.cost,
            'response_time': self.response_time,
            'created_at': self.created_at.isoformat(),
            # Week 4 Context Management fields
            'request_tokens': self.request_tokens,
            'response_tokens': self.response_tokens,
            'total_tokens': self.total_tokens,
            'cumulative_tokens': self.cumulative_tokens,
            'context_length': self.context_length
        }

# UNUSED MODEL - Commented out during cleanup (Week 1)
# class SystemLog(db.Model):
#     """Model for storing system logs and events"""
#     __tablename__ = 'system_logs'
#
#     id: Mapped[int] = mapped_column(Integer, primary_key=True)
#     level: Mapped[str] = mapped_column(String(20), nullable=False)  # INFO, WARNING, ERROR, etc.
#     message: Mapped[str] = mapped_column(Text, nullable=False)
#     module: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
#     function: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
#     extra_data: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
#     created_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc))
#
#     def __repr__(self):
#         return f'<SystemLog {self.level}: {self.message[:50]}...>'
#
#     def to_dict(self):
#         """Convert to dictionary for JSON serialization"""
#         return {
#             'id': self.id,
#             'level': self.level,
#             'message': self.message,
#             'module': self.module,
#             'function': self.function,
#             'extra_data': self.extra_data,
#             'created_at': self.created_at.isoformat()
#         }
