#!/usr/bin/env python3
"""
Test script to verify the Flask AI App setup
"""
import os
import sys
import asyncio
from pathlib import Path

def test_imports():
    """Test that all required modules can be imported"""
    print("Testing imports...")
    
    try:
        from app import create_app
        print("✓ Flask app imports successfully")
    except ImportError as e:
        print(f"✗ Failed to import Flask app: {e}")
        return False
    
    try:
        from models import db, Job, File  # Tool model commented out during cleanup
        print("✓ Models import successfully")
    except ImportError as e:
        print(f"✗ Failed to import models: {e}")
        return False
    
    try:
        from tools.file_tools import FileTools, FileReadRequest
        from tools.llm_tools import LLMTools, LLMRequest
        from tools.diff_tools import DiffTools, DiffRequest
        print("✓ Tools import successfully")
    except ImportError as e:
        print(f"✗ Failed to import tools: {e}")
        return False
    
    return True

def test_app_creation():
    """Test that the Flask app can be created"""
    print("\nTesting app creation...")
    
    try:
        from app import create_app
        app = create_app()
        print("✓ Flask app created successfully")
        return True, app
    except Exception as e:
        print(f"✗ Failed to create Flask app: {e}")
        return False, None

def test_database_setup(app):
    """Test database initialization"""
    print("\nTesting database setup...")
    
    try:
        with app.app_context():
            from models import db
            db.create_all()
            print("✓ Database tables created successfully")
            return True
    except Exception as e:
        print(f"✗ Failed to create database tables: {e}")
        return False

async def test_file_tools(app):
    """Test file tools functionality"""
    print("\nTesting file tools...")

    try:
        from tools.file_tools import FileTools, FileReadRequest, FileWriteRequest

        with app.app_context():
            # Test file write
            test_content = "Hello, Flask AI App!"
            test_file = "test_file.txt"

            write_request = FileWriteRequest(
                file_path=test_file,
                content=test_content
            )

            write_response = await FileTools.write_file(write_request)
            print(f"✓ File write test passed: {write_response.bytes_written} bytes written")

            # Test file read
            read_request = FileReadRequest(file_path=test_file)
            read_response = await FileTools.read_file(read_request)

            if read_response.content == test_content:
                print("✓ File read test passed")
            else:
                print("✗ File read test failed: content mismatch")
                return False

            # Clean up
            if os.path.exists(test_file):
                os.remove(test_file)
                print("✓ Test file cleaned up")

        return True

    except Exception as e:
        print(f"✗ File tools test failed: {e}")
        return False

async def test_diff_tools():
    """Test diff tools functionality"""
    print("\nTesting diff tools...")
    
    try:
        from tools.diff_tools import DiffTools, DiffRequest
        
        original = "Hello World\nThis is line 2\nThis is line 3"
        modified = "Hello Universe\nThis is line 2\nThis is line 4"
        
        diff_request = DiffRequest(
            original=original,
            modified=modified
        )
        
        diff_response = await DiffTools.generate_diff(diff_request)
        
        if diff_response.has_changes:
            print("✓ Diff generation test passed")
            print(f"  - {diff_response.additions} additions, {diff_response.deletions} deletions")
            print(f"  - Similarity ratio: {diff_response.similarity_ratio}")
            return True
        else:
            print("✗ Diff generation test failed: no changes detected")
            return False
            
    except Exception as e:
        print(f"✗ Diff tools test failed: {e}")
        return False

def test_configuration():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        from config import config, DevelopmentConfig
        
        dev_config = config['development']
        if dev_config == DevelopmentConfig:
            print("✓ Configuration loading test passed")
            return True
        else:
            print("✗ Configuration loading test failed")
            return False
            
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def check_environment():
    """Check environment setup"""
    print("\nChecking environment...")
    
    # Check Python version
    python_version = sys.version_info
    if python_version >= (3, 8):
        print(f"✓ Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"✗ Python version too old: {python_version.major}.{python_version.minor}.{python_version.micro}")
        return False
    
    # Check required files
    required_files = [
        'app.py',
        'models.py',
        'config.py',
        'requirements.txt',
        'tools/__init__.py',
        'tools/file_tools.py',
        'tools/llm_tools.py',
        'tools/diff_tools.py',
        'templates/base.html',
        'templates/index.html'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"✗ Missing files: {', '.join(missing_files)}")
        return False
    else:
        print("✓ All required files present")
    
    return True

async def main():
    """Run all tests"""
    print("Flask AI App Setup Test")
    print("=" * 50)
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed")
        return False
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed")
        return False
    
    # Test configuration
    if not test_configuration():
        print("\n❌ Configuration tests failed")
        return False
    
    # Test app creation
    success, app = test_app_creation()
    if not success:
        print("\n❌ App creation tests failed")
        return False
    
    # Test database setup
    if not test_database_setup(app):
        print("\n❌ Database setup tests failed")
        return False
    
    # Test file tools
    if not await test_file_tools(app):
        print("\n❌ File tools tests failed")
        return False
    
    # Test diff tools
    if not await test_diff_tools():
        print("\n❌ Diff tools tests failed")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All tests passed! Your Flask AI App is ready to run.")
    print("\nNext steps:")
    print("1. Set up your .env file with OPENROUTER_API_KEY")
    print("2. Run: python app.py")
    print("3. Visit: http://localhost:5000")
    
    return True

if __name__ == "__main__":
    asyncio.run(main())
