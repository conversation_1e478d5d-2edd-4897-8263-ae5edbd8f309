#!/usr/bin/env python3
"""
Test the new unified AI agent
"""
import asyncio
import httpx

async def test_unified_agent():
    """Test the unified AI agent with different types of requests"""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print("🤖 Testing Unified AI Agent...")
            
            # Test 1: Simple greeting (should use chat_response tool)
            print("\n1. Testing greeting...")
            response = await client.post(
                'http://localhost:5000/api/unified-ai/process',
                headers={'Content-Type': 'application/json'},
                json={
                    "prompt": "Hello! How are you?",
                    "conversation_id": None,
                    "context": {}
                }
            )
            
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"Response: {data.get('response', 'No response')}")
                print(f"Success: {data.get('success', False)}")
            else:
                print(f"Error: {response.text}")
            
            # Test 2: File creation request (should use create_single_file tool)
            print("\n2. Testing single file creation...")
            response = await client.post(
                'http://localhost:5000/api/unified-ai/process',
                headers={'Content-Type': 'application/json'},
                json={
                    "prompt": "Create a Python script called hello.py that prints hello world",
                    "conversation_id": None,
                    "context": {}
                }
            )
            
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"Response: {data.get('response', 'No response')}")
                print(f"Success: {data.get('success', False)}")
            else:
                print(f"Error: {response.text}")
            
            # Test 3: Website creation request (should use create_website tool)
            print("\n3. Testing website creation...")
            response = await client.post(
                'http://localhost:5000/api/unified-ai/process',
                headers={'Content-Type': 'application/json'},
                json={
                    "prompt": "Create a beautiful website for a coffee shop called Bean There Café",
                    "conversation_id": None,
                    "context": {}
                }
            )
            
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"Response: {data.get('response', 'No response')}")
                print(f"Success: {data.get('success', False)}")
            else:
                print(f"Error: {response.text}")
            
            # Test 4: File listing request (should use list_directory_files tool)
            print("\n4. Testing file listing...")
            response = await client.post(
                'http://localhost:5000/api/unified-ai/process',
                headers={'Content-Type': 'application/json'},
                json={
                    "prompt": "What files are in my project?",
                    "conversation_id": None,
                    "context": {}
                }
            )

            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"Response: {data.get('response', 'No response')}")
                print(f"Success: {data.get('success', False)}")
            else:
                print(f"Error: {response.text}")

            # Test 5: File reading request (should use read_file_content tool)
            print("\n5. Testing file reading...")
            response = await client.post(
                'http://localhost:5000/api/unified-ai/process',
                headers={'Content-Type': 'application/json'},
                json={
                    "prompt": "Show me the content of index.html",
                    "conversation_id": None,
                    "context": {}
                }
            )
            
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"Response: {data.get('response', 'No response')}")
                print(f"Success: {data.get('success', False)}")
            else:
                print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_unified_agent())
