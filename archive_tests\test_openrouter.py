#!/usr/bin/env python3
"""
Test OpenRouter API connection
"""
import os
import asyncio
import httpx
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_openrouter_connection():
    """Test OpenRouter API connection"""
    api_key = os.getenv('OPENROUTER_API_KEY')
    base_url = 'https://openrouter.ai/api/v1'
    
    if not api_key:
        print("❌ OPENROUTER_API_KEY not found in environment")
        return False
    
    print(f"🔑 API Key found: {api_key[:20]}...")
    
    try:
        # Test 1: Get available models
        print("\n📋 Testing models endpoint...")
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(
                f"{base_url}/models",
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                }
            )
            
            if response.status_code == 200:
                models = response.json()
                print(f"✅ Models endpoint working! Found {len(models.get('data', []))} models")
                
                # Show first few models
                for model in models.get('data', [])[:3]:
                    print(f"   - {model.get('id', 'Unknown')}")
            else:
                print(f"❌ Models endpoint failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
        
        # Test 2: Simple chat completion
        print("\n💬 Testing chat completion...")
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{base_url}/chat/completions",
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json",
                    "HTTP-Referer": "http://localhost:5000",
                    "X-Title": "Flask AI App Test"
                },
                json={
                    "model": "deepseek/deepseek-r1-0528:free",
                    "messages": [
                        {"role": "user", "content": "Hello! Just testing the connection. Please respond with 'Connection successful!'"}
                    ],
                    "max_tokens": 50
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                message = result['choices'][0]['message']['content']
                usage = result.get('usage', {})
                
                print(f"✅ Chat completion working!")
                print(f"📝 Response: {message}")
                print(f"🔢 Tokens used: {usage.get('total_tokens', 'Unknown')}")
                return True
            else:
                print(f"❌ Chat completion failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing OpenRouter: {e}")
        return False

async def main():
    """Main test function"""
    print("OpenRouter API Connection Test")
    print("=" * 40)
    
    success = await test_openrouter_connection()
    
    if success:
        print("\n🎉 OpenRouter API is working correctly!")
        print("Your Flask AI App should now be able to chat with AI models.")
    else:
        print("\n❌ OpenRouter API test failed.")
        print("Please check your API key and try again.")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
