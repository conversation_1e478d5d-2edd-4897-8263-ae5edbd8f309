#!/usr/bin/env python3
"""
Demo file for testing the Flask AI App file operations
"""

def hello_world():
    """A simple hello world function"""
    return "Hello from Flask AI App!"

def calculate_fibonacci(n):
    """Calculate the nth Fibonacci number"""
    if n <= 1:
        return n
    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)

def main():
    """Main function"""
    print(hello_world())
    
    # Calculate first 10 Fibonacci numbers
    print("First 10 Fibonacci numbers:")
    for i in range(10):
        print(f"F({i}) = {calculate_fibonacci(i)}")

if __name__ == "__main__":
    main()
