/**
 * Project Management System
 * Handles project creation, switching, and UI updates
 */

class ProjectManager {
    constructor() {
        this.currentProject = null;
        this.projects = [];
        this.init();
    }

    async init() {
        await this.loadCurrentProject();
        this.setupEventListeners();
        this.updateUI();
    }

    setupEventListeners() {
        // New Project button
        const newProjectBtn = document.getElementById('new-project-button');
        if (newProjectBtn) {
            newProjectBtn.addEventListener('click', () => this.showNewProjectModal());
        }

        // Switch Project button
        const switchProjectBtn = document.getElementById('switch-project-button');
        if (switchProjectBtn) {
            switchProjectBtn.addEventListener('click', () => this.showProjectsModal());
        }
    }

    async loadCurrentProject() {
        try {
            const response = await AppUtils.apiRequest('/api/projects/current');
            this.currentProject = response.current_project;
        } catch (error) {
            console.warn('No current project set');
            this.currentProject = null;
        }
    }

    async loadProjects() {
        try {
            const response = await AppUtils.apiRequest('/api/projects');
            this.projects = response.projects;
            return this.projects;
        } catch (error) {
            console.error('Failed to load projects:', error);
            return [];
        }
    }

    updateUI() {
        const projectNameNav = document.getElementById('project-name-nav');
        if (projectNameNav) {
            if (this.currentProject) {
                projectNameNav.textContent = `📁 ${this.currentProject.name}`;
                projectNameNav.title = this.currentProject.description || this.currentProject.name;
            } else {
                projectNameNav.textContent = 'No Project';
                projectNameNav.title = 'Create or select a project to get started';
            }
        }
    }

    showNewProjectModal() {
        const modal = this.createNewProjectModal();
        document.body.appendChild(modal);
    }

    createNewProjectModal() {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        modal.innerHTML = `
            <div class="bg-white rounded-lg max-w-md w-full">
                <div class="p-6 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">📁 Create New Project</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label for="project-name" class="block text-sm font-medium text-gray-700 mb-1">
                                Project Name *
                            </label>
                            <input
                                type="text"
                                id="project-name"
                                placeholder="My Awesome Project"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                                required
                            >
                        </div>
                        <div>
                            <label for="project-description" class="block text-sm font-medium text-gray-700 mb-1">
                                Description (optional)
                            </label>
                            <textarea
                                id="project-description"
                                placeholder="Brief description of your project..."
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                                rows="3"
                            ></textarea>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button
                            id="cancel-project-btn"
                            class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                        >
                            Cancel
                        </button>
                        <button
                            id="create-project-btn"
                            class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                        >
                            Create Project
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Event listeners
        modal.querySelector('#cancel-project-btn').addEventListener('click', () => {
            modal.remove();
        });

        modal.querySelector('#create-project-btn').addEventListener('click', async () => {
            const name = modal.querySelector('#project-name').value.trim();
            const description = modal.querySelector('#project-description').value.trim();

            if (!name) {
                AppUtils.showNotification('Project name is required', 'error');
                return;
            }

            try {
                const response = await AppUtils.apiRequest('/api/projects', {
                    method: 'POST',
                    body: JSON.stringify({ name, description })
                });

                this.currentProject = response.project;
                this.updateUI();
                modal.remove();
                
                AppUtils.showNotification(`Project "${name}" created successfully!`, 'success');
                
                // Refresh file editor if it exists
                if (window.fileEditor) {
                    window.fileEditor.refreshFileTree();
                }
                
            } catch (error) {
                AppUtils.showNotification('Failed to create project', 'error');
            }
        });

        // Close on outside click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        return modal;
    }

    async showProjectsModal() {
        const projects = await this.loadProjects();
        const modal = this.createProjectsModal(projects);
        document.body.appendChild(modal);
    }

    createProjectsModal(projects) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        
        const projectsList = projects.length > 0 ? projects.map(project => {
            const isActive = this.currentProject && this.currentProject.id === project.id;
            return `
                <div class="border rounded-lg p-4 hover:bg-gray-50 ${isActive ? 'bg-blue-50 border-blue-200' : ''}">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2">
                                <h4 class="font-medium text-gray-900">
                                    ${isActive ? '🔵 ' : '📁 '}${project.name}
                                </h4>
                                <span class="text-xs text-gray-500">${project.file_count} files</span>
                            </div>
                            ${project.description ? `<p class="text-sm text-gray-600 mt-1">${project.description}</p>` : ''}
                            <p class="text-xs text-gray-400 mt-1">
                                Created: ${new Date(project.created_at).toLocaleDateString()}
                            </p>
                        </div>
                        <div class="flex space-x-2">
                            ${!isActive ? `
                                <button
                                    class="switch-project-btn px-3 py-1 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700"
                                    data-project-id="${project.id}"
                                >
                                    Switch
                                </button>
                            ` : `
                                <span class="px-3 py-1 text-sm bg-green-100 text-green-800 rounded">
                                    Active
                                </span>
                            `}
                            <button
                                class="delete-project-btn px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700"
                                data-project-id="${project.id}"
                                data-project-name="${project.name}"
                            >
                                Delete
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }).join('') : `
            <div class="text-center text-gray-500 py-8">
                <p>No projects yet</p>
                <p class="text-sm">Create your first project to get started!</p>
            </div>
        `;

        modal.innerHTML = `
            <div class="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] flex flex-col">
                <div class="p-6 border-b flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">🔄 Manage Projects</h3>
                    <button id="close-projects-modal" class="text-gray-400 hover:text-gray-600">
                        ✕
                    </button>
                </div>
                <div class="flex-1 overflow-y-auto p-6">
                    <div class="space-y-3">
                        ${projectsList}
                    </div>
                </div>
                <div class="p-6 border-t">
                    <button
                        id="new-project-from-modal"
                        class="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                    >
                        📁 Create New Project
                    </button>
                </div>
            </div>
        `;

        // Event listeners
        modal.querySelector('#close-projects-modal').addEventListener('click', () => {
            modal.remove();
        });

        modal.querySelector('#new-project-from-modal').addEventListener('click', () => {
            modal.remove();
            this.showNewProjectModal();
        });

        // Switch project buttons
        modal.querySelectorAll('.switch-project-btn').forEach(btn => {
            btn.addEventListener('click', async () => {
                const projectId = btn.dataset.projectId;
                await this.switchProject(projectId);
                modal.remove();
            });
        });

        // Delete project buttons
        modal.querySelectorAll('.delete-project-btn').forEach(btn => {
            btn.addEventListener('click', async () => {
                const projectId = btn.dataset.projectId;
                const projectName = btn.dataset.projectName;
                
                if (confirm(`Are you sure you want to delete project "${projectName}"? This will delete all files in the project.`)) {
                    await this.deleteProject(projectId);
                    // Refresh the modal
                    modal.remove();
                    this.showProjectsModal();
                }
            });
        });

        // Close on outside click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        return modal;
    }

    async switchProject(projectId) {
        try {
            const response = await AppUtils.apiRequest(`/api/projects/${projectId}/switch`, {
                method: 'POST'
            });

            this.currentProject = response.project;
            this.updateUI();
            
            AppUtils.showNotification(`Switched to project "${response.project.name}"`, 'success');
            
            // Refresh file editor if it exists
            if (window.fileEditor) {
                window.fileEditor.refreshFileTree();
            }
            
        } catch (error) {
            AppUtils.showNotification('Failed to switch project', 'error');
        }
    }

    async deleteProject(projectId) {
        try {
            await AppUtils.apiRequest(`/api/projects/${projectId}`, {
                method: 'DELETE'
            });

            // If we deleted the current project, clear it
            if (this.currentProject && this.currentProject.id === projectId) {
                this.currentProject = null;
                this.updateUI();
            }
            
            AppUtils.showNotification('Project deleted successfully', 'success');
            
        } catch (error) {
            AppUtils.showNotification('Failed to delete project', 'error');
        }
    }
}

// Initialize project manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.projectManager = new ProjectManager();
});
