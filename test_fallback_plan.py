#!/usr/bin/env python3
"""
Test the fallback plan directly to isolate the bug
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.unified_agent import get_unified_agent

async def test_fallback_plan():
    """Test the fallback plan creation directly"""
    print("🔧 **TESTING FALLBACK PLAN DIRECTLY**\n")
    
    try:
        agent = get_unified_agent()
        print("✅ Agent initialized successfully")
        
        # Test the enhanced fallback plan creation
        prompt = "create a very creative website"
        print(f"🎯 **Testing Fallback Plan for:** {prompt}")
        
        # Create the fallback plan directly
        fallback_plan = agent.workflow_analyzer._create_enhanced_fallback_plan(prompt)
        
        print(f"📋 **Fallback Plan Created:**")
        print(f"   Title: {fallback_plan.title}")
        print(f"   Steps: {fallback_plan.total_steps}")
        
        # Check each step
        for i, step in enumerate(fallback_plan.steps, 1):
            print(f"\n   Step {i}:")
            print(f"     Type: {type(step)}")
            print(f"     Action: {getattr(step, 'action_type', 'MISSING')}")
            print(f"     Description: {getattr(step, 'description', 'MISSING')}")
            print(f"     File Path: {getattr(step, 'file_path', 'MISSING')}")
            print(f"     Time: {getattr(step, 'estimated_time', 'MISSING')}")
        
        # Now test the action queue conversion
        print(f"\n🔄 **Testing Action Queue Conversion:**")
        
        # Clear any existing actions
        agent.action_queue.clear_queue()
        
        # Convert plan to actions
        agent.action_queue._convert_plan_to_actions(fallback_plan)
        
        # Check the actions
        print(f"📊 **Actions Created:** {len(agent.action_queue.actions)}")
        
        for i, action in enumerate(agent.action_queue.actions, 1):
            print(f"\n   Action {i}:")
            print(f"     ID: {action.id}")
            print(f"     Type: {action.action_type}")
            print(f"     Description: {action.description}")
            print(f"     File Path: {action.file_path}")
            print(f"     Time: {action.estimated_time}")
        
        # Test execution
        print(f"\n🚀 **Testing Workflow Execution:**")
        
        results = []
        async for action_result in agent.action_queue.execute_workflow(fallback_plan):
            print(f"✅ Action completed: {action_result.action_id}")
            print(f"   Status: {action_result.status.value}")
            print(f"   Result: {action_result.result}")
            results.append(action_result)
        
        print(f"\n🎉 **Execution Complete!** {len(results)} actions executed.")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run fallback plan test"""
    success = await test_fallback_plan()
    
    if success:
        print("\n🎉 **FALLBACK PLAN TEST SUCCESSFUL!**")
        print("✅ Fallback plan creation working")
        print("✅ Action queue conversion working")
        print("✅ Workflow execution working")
    else:
        print("\n❌ **FALLBACK PLAN TEST FAILED!**")
        print("Found the source of the bug!")

if __name__ == "__main__":
    asyncio.run(main())
