#!/usr/bin/env python3
"""
Test the database persistence functionality
"""
import asyncio
import httpx
import time

async def test_conversation_persistence():
    """Test that conversations are saved and can be retrieved"""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing Conversation Persistence")
    print("=" * 50)
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print("📝 Sending first message...")
            
            # Send first message
            response1 = await client.post(
                f"{base_url}/api/llm/query",
                headers={"Content-Type": "application/json"},
                json={
                    "prompt": "Hello! Please say 'Hello from the AI!' and nothing else.",
                    "model": "deepseek/deepseek-r1-0528:free"
                }
            )
            
            if response1.status_code != 200:
                print(f"❌ Error: {response1.status_code}")
                print(f"Response: {response1.text}")
                return
            
            result1 = response1.json()
            conversation_id = result1.get('conversation_id')
            
            print(f"✅ First message sent successfully")
            print(f"🆔 Conversation ID: {conversation_id}")
            print(f"📝 Response: {result1.get('response', '')[:100]}...")
            
            # Wait a moment
            await asyncio.sleep(1)
            
            print("\n📝 Sending second message in same conversation...")
            
            # Send second message with same conversation ID
            response2 = await client.post(
                f"{base_url}/api/llm/query",
                headers={"Content-Type": "application/json"},
                json={
                    "prompt": "What was your previous message?",
                    "model": "deepseek/deepseek-r1-0528:free",
                    "conversation_id": conversation_id
                }
            )
            
            if response2.status_code != 200:
                print(f"❌ Error: {response2.status_code}")
                return
            
            result2 = response2.json()
            print(f"✅ Second message sent successfully")
            print(f"📝 Response: {result2.get('response', '')[:100]}...")
            
            # Wait a moment
            await asyncio.sleep(1)
            
            print("\n📋 Testing conversation history retrieval...")
            
            # Get conversation history
            history_response = await client.get(
                f"{base_url}/api/llm/conversations/{conversation_id}"
            )
            
            if history_response.status_code != 200:
                print(f"❌ Error getting history: {history_response.status_code}")
                return
            
            history = history_response.json()
            print(f"✅ Retrieved conversation history")
            print(f"📊 Number of messages: {len(history)}")
            
            for i, msg in enumerate(history):
                print(f"  {i+1}. {msg['role']}: {msg['content'][:50]}...")
            
            print("\n📋 Testing conversations list...")
            
            # Get conversations list
            list_response = await client.get(f"{base_url}/api/llm/conversations")
            
            if list_response.status_code != 200:
                print(f"❌ Error getting conversations list: {list_response.status_code}")
                return
            
            conversations_data = list_response.json()
            conversations = conversations_data.get('conversations', [])
            
            print(f"✅ Retrieved conversations list")
            print(f"📊 Number of conversations: {len(conversations)}")
            
            if conversations:
                latest_conv = conversations[0]  # Should be most recent
                print(f"📝 Latest conversation:")
                print(f"  🆔 ID: {latest_conv['conversation_id']}")
                print(f"  💬 Messages: {latest_conv['message_count']}")
                print(f"  🎯 Tokens: {latest_conv['total_tokens']}")
                print(f"  💰 Cost: ${latest_conv['total_cost']:.6f}")
                print(f"  📅 Last message: {latest_conv['last_message_at']}")
            
            print(f"\n🎉 All persistence tests passed!")
            print("💡 Key features working:")
            print("  ✅ Conversations are saved to database")
            print("  ✅ Conversation history is maintained")
            print("  ✅ Messages can be retrieved")
            print("  ✅ Conversation metadata is tracked")
            print("  ✅ Multiple conversations are supported")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")

async def main():
    """Main test function"""
    print("🚀 Starting Conversation Persistence Test")
    print("Make sure the Flask app is running at http://localhost:5000")
    print("This test will create a conversation and verify it's saved.\n")
    
    await test_conversation_persistence()
    
    print("\n✨ Test complete!")
    print("🔄 Try refreshing your browser - the conversation should persist!")

if __name__ == "__main__":
    asyncio.run(main())
