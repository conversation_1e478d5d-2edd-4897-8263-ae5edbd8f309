# Flask AI App

A single-unit Flask application with SQLite, OpenRouter AI integration, and APScheduler for background jobs. Built following the lean roadmap for rapid development.

## Features

- **Async Flask Routes**: Modern async/await support for non-blocking operations
- **OpenRouter Integration**: Connect to multiple LLM models via OpenRouter API
- **File Management Tools**: Read, write, and diff files with Pydantic validation
- **Background Jobs**: APScheduler with SQLite job store for async task processing
- **Clean UI**: Split-pane developer interface with chat and code editor
- **Type Safety**: Pydantic models for request/response validation
- **SQLite Database**: Lightweight database with Flask-SQLAlchemy

## Quick Start

### 1. Clone and Setup

```bash
git clone <your-repo-url>
cd flask-ai-app

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings
# Most importantly, add your OpenRouter API key:
OPENROUTER_API_KEY=your-api-key-here
```

### 3. Initialize Database

```bash
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all()"
```

### 4. Run the Application

```bash
# Development mode
python app.py

# Or using Flask CLI
flask run
```

Visit `http://localhost:5000` to access the application.

## Project Structure

```
flask-ai-app/
├── app.py                 # Main application factory
├── config.py             # Configuration settings
├── models.py             # SQLAlchemy models
├── scheduler.py          # Background job scheduler
├── requirements.txt      # Python dependencies
├── .env.example         # Environment variables template
├── tools/               # Pydantic-wrapped endpoints
│   ├── __init__.py
│   ├── file_tools.py    # File manipulation tools
│   ├── llm_tools.py     # LLM interaction tools
│   └── diff_tools.py    # Text comparison tools
├── templates/           # Jinja2 templates
│   ├── base.html        # Base template
│   └── index.html       # Main chat interface
└── static/             # CSS, JS, images (optional)
```

## API Endpoints

### File Tools
- `POST /api/tools/read_file` - Read file content
- `POST /api/tools/write_file` - Write file content
- `POST /api/tools/list_files` - List directory contents
- `POST /api/tools/edit_diff` - Generate diff between texts

### LLM Tools
- `POST /api/llm/query` - Query OpenRouter LLM
- `GET /api/llm/models` - Get available models
- `POST /api/llm/stream` - Stream LLM response

### Job Management
- `GET /api/jobs` - List all jobs
- `GET /api/jobs/<job_id>` - Get job details
- `POST /api/jobs/schedule` - Schedule background job
- `DELETE /api/jobs/<job_id>` - Cancel job

## Usage Examples

### Chat with AI
1. Open the application in your browser
2. Type your question in the chat input
3. Select your preferred model (DeepSeek, GPT-3.5, GPT-4)
4. Click Send or press Enter

### File Operations
1. Click "Open" in the file editor panel
2. Enter a file path (e.g., `app.py`)
3. Edit the content in the code editor
4. Click "Save" or press Ctrl+S

### Background Jobs
```python
# Schedule an LLM job
job_id = await scheduler.schedule_llm_job(
    prompt="Explain this code",
    model="deepseek/deepseek-chat",
    delay_seconds=10
)

# Schedule file processing
job_id = await scheduler.schedule_file_processing_job(
    file_path="large_file.txt",
    operation="analyze"
)
```

## Configuration Options

### Environment Variables
- `OPENROUTER_API_KEY`: Your OpenRouter API key (required)
- `DATABASE_URL`: SQLite database path (default: sqlite:///app.db)
- `SECRET_KEY`: Flask secret key for sessions
- `MAX_FILE_SIZE`: Maximum file size in bytes (default: 10MB)
- `LOG_LEVEL`: Logging level (default: INFO)

### Supported Models
- DeepSeek Chat (`deepseek/deepseek-chat`)
- OpenAI GPT-3.5 Turbo (`openai/gpt-3.5-turbo`)
- OpenAI GPT-4 (`openai/gpt-4`)
- And many more via OpenRouter

## Development

### Adding New Tools
1. Create a new tool class in the `tools/` directory
2. Define Pydantic models for request/response validation
3. Implement async methods with proper error handling
4. Register routes in `app.py`

### Database Migrations
```bash
# Create new migration
flask db init
flask db migrate -m "Description"
flask db upgrade
```

### Testing
```bash
# Run tests
pytest

# Run with coverage
pytest --cov=app
```

## Production Deployment

### Using Gunicorn + Uvicorn
```bash
pip install gunicorn uvicorn

# Run with ASGI support
gunicorn -w 4 -k uvicorn.workers.UvicornWorker app:app
```

### Using Docker
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 5000
CMD ["gunicorn", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "app:app", "--bind", "0.0.0.0:5000"]
```

## Troubleshooting

### Common Issues

1. **OpenRouter API Key Error**
   - Ensure your API key is set in the `.env` file
   - Check that the key has sufficient credits

2. **Database Connection Error**
   - Run the database initialization command
   - Check file permissions for SQLite database

3. **File Permission Errors**
   - Ensure the application has read/write permissions
   - Check the `ALLOWED_FILE_EXTENSIONS` setting

4. **Async Route Issues**
   - Make sure you're using an ASGI server for production
   - Use `await` for all async operations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with tests
4. Submit a pull request

## License

MIT License - see LICENSE file for details.
