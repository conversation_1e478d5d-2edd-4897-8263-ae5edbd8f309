"""
AI Activity Tracker - Intelligent real-time activity visualization
Provides AI-powered categorization of agent activities with animations
"""

import logging
import asyncio
import re
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ActivityType(Enum):
    """AI-determined activity types with corresponding animations"""
    THINKING = "thinking"
    ANALYZING = "analyzing"
    CODING = "coding"
    WRITING = "writing"
    READING = "reading"
    MODIFYING = "modifying"
    CREATING = "creating"
    SEARCHING = "searching"
    CALCULATING = "calculating"
    DEBUGGING = "debugging"
    PLANNING = "planning"
    OPTIMIZING = "optimizing"
    TESTING = "testing"
    COMPILING = "compiling"
    DEPLOYING = "deploying"
    LEARNING = "learning"
    PROCESSING = "processing"
    GENERATING = "generating"
    VALIDATING = "validating"
    FINALIZING = "finalizing"

@dataclass
class ActivityEvent:
    """Represents a single AI activity event"""
    activity_type: ActivityType
    description: str
    timestamp: datetime
    progress: Optional[float] = None  # 0.0 to 1.0
    metadata: Optional[Dict[str, Any]] = None

class IntelligentActivityTracker:
    """AI-powered activity tracker with intelligent categorization"""
    
    def __init__(self):
        self.current_activity: Optional[ActivityEvent] = None
        self.activity_history: List[ActivityEvent] = []
        self.activity_callbacks: List[Callable[[ActivityEvent], None]] = []
        self.is_active = False
        
        # AI-powered activity patterns for intelligent categorization
        self.activity_patterns = {
            ActivityType.THINKING: [
                r'thinking|pondering|considering|contemplating|reasoning',
                r'analyzing request|understanding|interpreting',
                r'determining|deciding|evaluating'
            ],
            ActivityType.ANALYZING: [
                r'analyzing|examining|investigating|studying|reviewing',
                r'parsing|inspecting|checking|scanning',
                r'looking at|reading through|going through'
            ],
            ActivityType.CODING: [
                r'coding|programming|implementing|developing',
                r'writing code|creating function|building|constructing',
                r'adding method|defining class|scripting'
            ],
            ActivityType.WRITING: [
                r'writing|composing|drafting|creating text',
                r'generating content|crafting|authoring',
                r'documenting|explaining|describing'
            ],
            ActivityType.READING: [
                r'reading|loading|fetching|retrieving',
                r'getting|accessing|opening|viewing',
                r'checking file|examining content'
            ],
            ActivityType.MODIFYING: [
                r'modifying|editing|updating|changing',
                r'altering|adjusting|tweaking|refining',
                r'improving|enhancing|fixing'
            ],
            ActivityType.CREATING: [
                r'creating|making|building|generating',
                r'establishing|setting up|initializing',
                r'constructing|forming|producing'
            ],
            ActivityType.SEARCHING: [
                r'searching|finding|looking for|seeking',
                r'querying|exploring|discovering',
                r'locating|hunting|browsing'
            ],
            ActivityType.CALCULATING: [
                r'calculating|computing|processing numbers',
                r'math|arithmetic|counting|measuring',
                r'estimating|determining size|quantifying'
            ],
            ActivityType.DEBUGGING: [
                r'debugging|troubleshooting|fixing|resolving',
                r'error|issue|problem|bug',
                r'diagnosing|investigating problem'
            ],
            ActivityType.PLANNING: [
                r'planning|organizing|structuring|designing',
                r'outlining|mapping|strategizing',
                r'preparing|arranging|coordinating'
            ],
            ActivityType.OPTIMIZING: [
                r'optimizing|improving|enhancing|refining',
                r'streamlining|efficiency|performance',
                r'tuning|polishing|perfecting'
            ],
            ActivityType.TESTING: [
                r'testing|verifying|validating|checking',
                r'running tests|executing|trying',
                r'confirming|ensuring|proving'
            ],
            ActivityType.COMPILING: [
                r'compiling|building|assembling|packaging',
                r'bundling|preparing build|processing',
                r'transforming|converting|rendering'
            ],
            ActivityType.DEPLOYING: [
                r'deploying|publishing|releasing|launching',
                r'installing|setting up|configuring',
                r'activating|enabling|starting'
            ],
            ActivityType.LEARNING: [
                r'learning|studying|researching|investigating',
                r'understanding|grasping|comprehending',
                r'acquiring knowledge|gathering information'
            ],
            ActivityType.PROCESSING: [
                r'processing|handling|managing|working with',
                r'dealing with|operating on|manipulating',
                r'transforming|converting|parsing'
            ],
            ActivityType.GENERATING: [
                r'generating|producing|creating|making',
                r'synthesizing|composing|building',
                r'crafting|forming|developing'
            ],
            ActivityType.VALIDATING: [
                r'validating|verifying|confirming|checking',
                r'ensuring|proving|testing validity',
                r'authenticating|authorizing|approving'
            ],
            ActivityType.FINALIZING: [
                r'finalizing|completing|finishing|wrapping up',
                r'concluding|ending|closing|done',
                r'ready|complete|finished'
            ]
        }
    
    def _categorize_activity(self, description: str, tool_name: str = None, context: str = None) -> ActivityType:
        """AI-powered intelligent activity categorization"""
        desc_lower = description.lower()
        tool_lower = (tool_name or "").lower()
        context_lower = (context or "").lower()
        
        # Combine all text for analysis
        full_text = f"{desc_lower} {tool_lower} {context_lower}".strip()
        
        logger.info(f"🧠 Categorizing activity: '{full_text}'")
        
        # Score each activity type based on pattern matching
        scores = {}
        for activity_type, patterns in self.activity_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, full_text))
                score += matches
            scores[activity_type] = score
        
        # Find the highest scoring activity type
        best_activity = max(scores.items(), key=lambda x: x[1])
        
        if best_activity[1] > 0:
            logger.info(f"🎯 Categorized as: {best_activity[0].value} (score: {best_activity[1]})")
            return best_activity[0]
        
        # Fallback logic based on tool names
        if tool_name:
            if 'file' in tool_lower or 'modify' in tool_lower:
                return ActivityType.MODIFYING
            elif 'create' in tool_lower or 'generate' in tool_lower:
                return ActivityType.CREATING
            elif 'search' in tool_lower or 'find' in tool_lower:
                return ActivityType.SEARCHING
            elif 'code' in tool_lower or 'script' in tool_lower:
                return ActivityType.CODING
        
        # Default fallback
        logger.info(f"🤔 Using default categorization: PROCESSING")
        return ActivityType.PROCESSING
    
    def start_activity(self, description: str, tool_name: str = None, context: str = None) -> ActivityEvent:
        """Start tracking a new AI activity with intelligent categorization"""
        activity_type = self._categorize_activity(description, tool_name, context)
        
        event = ActivityEvent(
            activity_type=activity_type,
            description=description,
            timestamp=datetime.now(),
            metadata={
                'tool_name': tool_name,
                'context': context
            }
        )
        
        self.current_activity = event
        self.activity_history.append(event)
        self.is_active = True
        
        logger.info(f"🚀 Started activity: {activity_type.value} - {description}")
        
        # Notify all callbacks
        for callback in self.activity_callbacks:
            try:
                callback(event)
            except Exception as e:
                logger.error(f"❌ Error in activity callback: {e}")
        
        return event
    
    def update_progress(self, progress: float, description: str = None):
        """Update the progress of the current activity"""
        if self.current_activity:
            self.current_activity.progress = max(0.0, min(1.0, progress))
            if description:
                self.current_activity.description = description
            
            logger.info(f"📊 Progress update: {progress:.1%} - {description or self.current_activity.description}")
            
            # Notify callbacks
            for callback in self.activity_callbacks:
                try:
                    callback(self.current_activity)
                except Exception as e:
                    logger.error(f"❌ Error in progress callback: {e}")
    
    def end_activity(self, final_description: str = None):
        """End the current activity"""
        if self.current_activity:
            if final_description:
                self.current_activity.description = final_description
            self.current_activity.progress = 1.0
            
            logger.info(f"✅ Completed activity: {self.current_activity.activity_type.value}")
            
            # Final callback notification
            for callback in self.activity_callbacks:
                try:
                    callback(self.current_activity)
                except Exception as e:
                    logger.error(f"❌ Error in completion callback: {e}")
            
            self.current_activity = None
            self.is_active = False
    
    def add_callback(self, callback: Callable[[ActivityEvent], None]):
        """Add a callback to be notified of activity events"""
        self.activity_callbacks.append(callback)
        logger.info(f"📡 Added activity callback: {callback.__name__}")
    
    def remove_callback(self, callback: Callable[[ActivityEvent], None]):
        """Remove an activity callback"""
        if callback in self.activity_callbacks:
            self.activity_callbacks.remove(callback)
            logger.info(f"📡 Removed activity callback: {callback.__name__}")
    
    def get_current_activity(self) -> Optional[ActivityEvent]:
        """Get the current activity event"""
        return self.current_activity
    
    def get_activity_history(self) -> List[ActivityEvent]:
        """Get the complete activity history"""
        return self.activity_history.copy()

# Global activity tracker instance
activity_tracker = IntelligentActivityTracker()
